<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Educasheer Al-Hikmah Study Lounge - Admission Form</title>
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Montserrat', sans-serif;
        }

        body {
            background: #1a2a6c;
            color: #333;
            min-height: 100vh;
            overflow-x: auto;
            position: relative;
            padding: 20px;
            font-family: 'Montserrat', sans-serif;
        }

        /*
         * Form-based poster for Educasheer Al-Hikmah Study Lounge
         * Optimized for 6ft x 3ft (72in x 36in) landscape orientation
         */
        .poster-container {
            position: relative;
            background: linear-gradient(135deg,
                #1a2a6c 0%,
                #b21f1f 50%,
                #fdbb2d 100%
            );
            background-size: 100% 100%;
            width: 100%;
            min-width: 1400px;
            margin: 0 auto 50px;
            position: relative;
            overflow: visible;
            box-sizing: border-box;
            page-break-inside: avoid;
            border-radius: 20px;
            box-shadow: 0 20px 80px rgba(0, 0, 0, 0.5);
            min-height: 100vh;
        }

        /* Removed animation keyframes for print-friendly version */

        .poster-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            /* Simplified pattern for better printing */
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="none" stroke="rgba(255,255,255,0.08)" stroke-width="1.5"/></svg>');
            opacity: 0.2;
            pointer-events: none;
            z-index: -1;
        }

        /* Decorative elements for the poster - optimized for print */
        .poster-glow {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
            border-radius: 20px;
            box-shadow: inset 0 0 80px rgba(253, 187, 45, 0.2);
        }

        .poster-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at center,
                rgba(255, 255, 255, 0.03) 0%,
                rgba(0, 0, 0, 0.1) 100%);
            opacity: 0.4;
            pointer-events: none;
            z-index: 2;
            border-radius: 20px;
        }

        .container {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            margin: 0 auto;
            padding: 20px 30px;
            display: grid;
            grid-template-columns: 28% 72%;
            grid-template-rows: auto 1fr;
            grid-template-areas:
                "header header"
                "sidebar main";
            box-sizing: border-box;
            gap: 20px;
            overflow: visible;
        }

        header {
            grid-area: header;
            padding: 20px 15px 15px;
            position: relative;
            /* More solid background for better printing */
            background: linear-gradient(90deg,
                rgba(26, 42, 108, 0.4) 0%,
                rgba(178, 31, 31, 0.3) 50%,
                rgba(253, 187, 45, 0.2) 100%);
            border-bottom: 2px solid rgba(253, 187, 45, 0.5);
            margin-bottom: 10px;
            border-radius: 15px 15px 0 0;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }

        .header-left {
            flex: 1;
            padding-right: 15px;
        }

        .header-right {
            flex: 1;
            padding-left: 15px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .header-center {
            flex: 2;
            text-align: center;
            padding: 0 20px;
        }

        .header-contact-info {
            margin: 0;
            padding: 15px;
            text-align: left;
            background: linear-gradient(135deg,
                rgba(26, 42, 108, 0.2) 0%,
                rgba(178, 31, 31, 0.15) 100%);
            border: 1px solid rgba(253, 187, 45, 0.3);
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(10px);
        }

        .header-contact-info p {
            margin: 7px 0;
            font-size: 15px;
            justify-content: flex-start;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        .header-qr-code {
            margin: 0 auto 12px;
            width: 130px;
            height: 130px;
            background: rgba(255, 255, 255, 0.9);
            padding: 5px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2), 0 0 15px rgba(253, 187, 45, 0.3);
            border: 2px solid rgba(253, 187, 45, 0.5);
        }

        .header-capacity-note {
            margin: 0;
            font-size: 16px;
            padding: 8px 12px;
            width: 90%;
            text-align: center;
            background: linear-gradient(90deg,
                rgba(178, 31, 31, 0.2) 0%,
                rgba(253, 187, 45, 0.2) 100%);
            border-radius: 8px;
            border: 1px solid rgba(253, 187, 45, 0.3);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .sidebar {
            grid-area: sidebar;
            display: flex;
            flex-direction: column;
            gap: 15px;
            overflow: visible;
            padding-right: 10px;
        }

        .main-content {
            grid-area: main;
            display: flex;
            flex-direction: column;
            gap: 15px;
            overflow: visible;
        }

        .logo {
            text-align: center;
            margin-bottom: 10px;
        }

        .logo-text {
            font-size: 32px;
            font-weight: 700;
            letter-spacing: 2px;
            margin-top: 8px;
            background: linear-gradient(to right, #b21f1f, #ffffff, #fdbb2d);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            text-transform: uppercase;
            text-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
            display: block; /* Show the text logo */
        }

        .tagline {
            font-size: 18px;
            font-style: italic;
            margin-top: 10px;
            color: #fdbb2d;
            letter-spacing: 1.5px;
            text-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
            font-weight: 500;
            position: relative;
            display: inline-block;
            padding: 0 25px;
        }

        .tagline::before,
        .tagline::after {
            content: '';
            position: absolute;
            top: 50%;
            width: 25px;
            height: 1px;
            background: linear-gradient(90deg, rgba(253, 187, 45, 0), rgba(253, 187, 45, 1));
        }

        .tagline::before {
            left: 0;
        }

        .tagline::after {
            right: 0;
            background: linear-gradient(90deg, rgba(253, 187, 45, 1), rgba(253, 187, 45, 0));
        }

        .logo-container {
            position: relative;
            margin-bottom: 5px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .logo-container::before {
            content: '';
            position: absolute;
            left: -25px;
            top: 50%;
            transform: translateY(-50%);
            width: 20px;
            height: 20px;
            background: #b21f1f;
            border-radius: 50%;
            box-shadow: 0 0 15px #b21f1f;
            animation: glowPulse 2s infinite alternate;
        }

        .logo-container::after {
            content: '';
            position: absolute;
            right: -25px;
            top: 50%;
            transform: translateY(-50%);
            width: 20px;
            height: 20px;
            background: #fdbb2d;
            border-radius: 50%;
            box-shadow: 0 0 15px #fdbb2d;
            animation: glowPulse 2s infinite alternate-reverse;
        }

        @keyframes starPulse {
            0% { opacity: 0.5; transform: translateY(-50%) scale(0.8); }
            100% { opacity: 1; transform: translateY(-50%) scale(1.1); }
        }

        .logo-img {
            width: 200px; /* Smaller logo as per user preference */
            height: auto;
            filter: drop-shadow(0 0 15px rgba(178, 31, 31, 0.7));
            animation: logoPulse 4s infinite ease-in-out;
            max-width: 90%;
            position: relative;
            z-index: 2;
            border-radius: 0; /* Remove border radius for logo as per user preference */
        }

        .logo-glow {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(253, 187, 45, 0.4) 0%, rgba(178, 31, 31, 0) 70%);
            z-index: 1;
            filter: blur(15px);
            animation: glowPulse 4s infinite alternate;
        }

        @keyframes logoPulse {
            0%, 100% { transform: scale(1); filter: brightness(1); }
            50% { transform: scale(1.03); filter: brightness(1.2); }
        }

        @keyframes glowPulse {
            0% { opacity: 0.4; transform: translate(-50%, -50%) scale(0.9); filter: blur(15px); }
            100% { opacity: 0.7; transform: translate(-50%, -50%) scale(1.1); filter: blur(20px); }
        }

        .section-title {
            font-size: 28px;
            text-align: center;
            margin: 10px 0 20px;
            color: #fff;
            text-transform: uppercase;
            letter-spacing: 2px;
            position: relative;
            padding-bottom: 12px;
            font-weight: 700;
            text-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
            background: linear-gradient(to right,
                #b21f1f 0%,
                #fdbb2d 30%,
                #ffffff 50%,
                #1a2a6c 70%,
                #b21f1f 100%);
            background-size: 200% auto;
            animation: shineText 4s linear infinite;
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }

        @keyframes shineText {
            to {
                background-position: 200% center;
            }
        }

        .sidebar .section-title {
            font-size: 24px;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 3px;
            background: linear-gradient(90deg, rgba(178, 31, 31, 0) 0%, rgba(253, 187, 45, 0.9) 50%, rgba(178, 31, 31, 0) 100%);
        }

        .section-icon {
            font-size: 22px;
            margin-bottom: 8px;
            color: #fdbb2d;
            display: block;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin: 15px 0;
            max-height: none;
            overflow: visible;
        }

        .sidebar .features-grid {
            grid-template-columns: 1fr;
            gap: 12px;
        }

        .main-content .features-grid {
            grid-template-columns: repeat(3, 1fr);
        }

        .feature {
            /* More solid background for better printing */
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.15) 0%,
                rgba(255, 255, 255, 0.08) 100%);
            backdrop-filter: blur(12px);
            border-radius: 15px;
            padding: 18px;
            transition: none; /* Remove transitions for print */
            border: 1px solid rgba(255, 255, 255, 0.25); /* More visible border for print */
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15); /* Lighter shadow for print */
            display: flex;
            align-items: flex-start;
            height: 100%;
            min-height: 0;
        }

        .feature::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            /* Simplified gradient for print */
            background: linear-gradient(90deg,
                rgba(255, 255, 255, 0) 0%,
                rgba(255, 255, 255, 0.03) 50%,
                rgba(255, 255, 255, 0) 100%);
            transition: none; /* Remove transitions for print */
        }

        /* Remove hover effects for print */
        .feature:hover::after {
            left: -100%; /* Don't animate for print */
        }

        .feature:hover {
            transform: none; /* No transform for print */
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.15) 0%,
                rgba(255, 255, 255, 0.08) 100%);
            border: 1px solid rgba(253, 187, 45, 0.4);
        }

        .feature-icon {
            font-size: 22px;
            background: linear-gradient(135deg, #fdbb2d, #b21f1f);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            margin-right: 15px;
            margin-top: 3px;
            text-shadow: 0 0 10px rgba(253, 187, 45, 0.5);
            flex-shrink: 0;
        }

        .feature-content {
            flex: 1;
        }

        .feature h3 {
            font-size: 18px;
            margin-bottom: 8px;
            color: #fdbb2d;
            font-weight: 600;
            letter-spacing: 0.5px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .feature p {
            font-size: 15px;
            line-height: 1.5;
            color: rgba(255, 255, 255, 0.9);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
            margin: 0;
        }

        .sidebar .feature h3 {
            font-size: 17px;
            margin-bottom: 6px;
        }

        .sidebar .feature p {
            font-size: 14px;
            line-height: 1.4;
        }

        .contact-info {
            text-align: center;
            margin: 10px 0;
            padding: 15px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 12px;
            border: 2px solid rgba(178, 31, 31, 0.3);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2), 0 0 20px rgba(253, 187, 45, 0.2) inset;
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        .contact-info::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle at center, rgba(253, 187, 45, 0.1) 0%, rgba(0, 0, 0, 0) 70%);
            opacity: 0.5;
            animation: rotateGradient 15s linear infinite;
            pointer-events: none;
        }

        @keyframes rotateGradient {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .contact-info p {
            margin: 6px 0;
            font-size: 14px;
            color: rgba(255, 255, 255, 1);
            letter-spacing: 0.5px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        .contact-info p i {
            margin-right: 8px;
            color: #fdbb2d;
            font-size: 16px;
            text-shadow: 0 0 10px rgba(253, 187, 45, 0.5);
        }

        .capacity-note {
            font-size: 16px;
            font-weight: 700;
            color: #fdbb2d;
            margin-top: 10px;
            text-transform: uppercase;
            letter-spacing: 1px;
            text-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(178, 31, 31, 0.1);
            padding: 8px;
            border-radius: 8px;
            border: 1px solid rgba(178, 31, 31, 0.2);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .capacity-note i {
            margin-right: 8px;
        }

        .qr-code {
            margin: 12px auto;
            padding: 10px;
            background: #fff;
            width: 110px;
            height: 110px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25);
            border: 2px solid #b21f1f;
            animation: qrGlow 3s infinite alternate;
        }

        @keyframes qrGlow {
            0% { box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25); }
            100% { box-shadow: 0 8px 30px rgba(253, 187, 45, 0.5); }
        }

        .qr-code::before {
            content: 'SCAN to PRE-REGISTER';
            position: absolute;
            bottom: -22px;
            left: 50%;
            transform: translateX(-50%);
            white-space: nowrap;
            color: #fff;
            font-weight: 600;
            font-size: 12px;
            text-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
        }

        .qr-placeholder {
            width: 100px;
            height: 100px;
            background: #000;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #fff;
            text-align: center;
            position: relative;
        }

        .qr-placeholder::after {
            content: '\f029';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            font-size: 36px;
            position: absolute;
            color: #fff;
            opacity: 0.5;
        }

        /* Glass morphism elements */
        .glass {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(12px);
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(178, 31, 31, 0.2);
            margin: 30px 0;
            transition: all 0.3s ease;
        }

        .glass:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
        }

        /* Simplified animations for print */
        @keyframes fadeUp {
            /* Simplified for print - no animation */
            from {
                opacity: 1;
                transform: translateY(0);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-up {
            /* No animation for print */
            opacity: 1 !important;
            transform: translateY(0) !important;
            animation: none;
        }

        /* Simplified decorative elements for print */
        .floating-shapes {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 0;
            overflow: hidden;
            border-radius: 20px;
            opacity: 0.3; /* Reduced opacity for print */
        }

        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.03);
            backdrop-filter: blur(5px);
            border-radius: 50%;
            animation: none; /* No animation for print */
            box-shadow: inset 0 0 20px rgba(178, 31, 31, 0.1);
            border: 1px solid rgba(253, 187, 45, 0.05);
        }

        .shape:nth-child(1) {
            width: 250px;
            height: 250px;
            top: 10%;
            left: 10%;
            animation-delay: 0s;
            background: radial-gradient(circle at 30% 40%, rgba(178, 31, 31, 0.1), rgba(178, 31, 31, 0.01));
        }

        .shape:nth-child(2) {
            width: 350px;
            height: 350px;
            top: 60%;
            left: 5%;
            animation-delay: 3s;
            background: radial-gradient(circle at 70% 20%, rgba(253, 187, 45, 0.1), rgba(253, 187, 45, 0.01));
        }

        .shape:nth-child(3) {
            width: 300px;
            height: 300px;
            top: 20%;
            right: 5%;
            animation-delay: 6s;
            background: radial-gradient(circle at 40% 50%, rgba(178, 31, 31, 0.1), rgba(178, 31, 31, 0.01));
        }

        .shape:nth-child(4) {
            width: 200px;
            height: 200px;
            top: 30%;
            right: 20%;
            animation-delay: 9s;
            background: radial-gradient(circle at 60% 30%, rgba(253, 187, 45, 0.1), rgba(253, 187, 45, 0.01));
        }

        .shape:nth-child(5) {
            width: 180px;
            height: 180px;
            bottom: 15%;
            right: 10%;
            animation-delay: 4s;
            background: radial-gradient(circle at 50% 50%, rgba(26, 42, 108, 0.1), rgba(26, 42, 108, 0.01));
        }

        .shape:nth-child(6) {
            width: 220px;
            height: 220px;
            bottom: 25%;
            left: 25%;
            animation-delay: 7s;
            background: radial-gradient(circle at 30% 70%, rgba(253, 187, 45, 0.1), rgba(253, 187, 45, 0.01));
        }

        @keyframes float {
            0% {
                transform: translate(0, 0) rotate(0deg) scale(1);
                opacity: 0.6;
            }
            25% {
                transform: translate(20px, 30px) rotate(5deg) scale(1.05);
                opacity: 0.7;
            }
            50% {
                transform: translate(35px, 55px) rotate(10deg) scale(1.1);
                opacity: 0.8;
            }
            75% {
                transform: translate(20px, 30px) rotate(5deg) scale(1.05);
                opacity: 0.7;
            }
            100% {
                transform: translate(0, 0) rotate(0deg) scale(1);
                opacity: 0.6;
            }
        }

        /* Responsive styles */
        @media (max-width: 768px) {
            .section-title {
                font-size: 28px;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }

            .feature h3 {
                font-size: 18px;
            }

            .feature p {
                font-size: 14px;
            }
        }

        /* Enhanced print styles for 6ft x 3ft (72in x 36in) poster */
        @media print {
            @page {
                size: 72in 36in; /* 6ft x 3ft */
                margin: 0;
            }

            body {
                /* White background for the page */
                background: #ffffff !important;
                color: #000;
                margin: 0;
                padding: 0;
            }

            .poster-container {
                border: none;
                box-shadow: none;
                width: 72in; /* 6ft */
                height: 36in; /* 3ft */
                max-width: 100%;
                margin: 0;
                padding: 0;
                /* Solid background colors for better printing */
                background-color: #1a2a6c !important;
                /* Optimized gradient for printing with higher saturation */
                background: linear-gradient(135deg,
                    #1a2a6c 0%,
                    #b21f1f 50%,
                    #fdbb2d 100%) !important;
                background-size: 100% 100% !important;
                page-break-inside: avoid;
                overflow: visible;
                position: relative;
                animation: none !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .container {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
            }

            /* Enhanced text rendering for print */
            .section-title {
                background: linear-gradient(to right, #b21f1f, #ffffff, #1a2a6c);
                -webkit-background-clip: text;
                background-clip: text;
                color: transparent;
                text-shadow: none !important;
            }

            .feature-icon {
                background: linear-gradient(135deg, #fdbb2d, #b21f1f);
                -webkit-background-clip: text;
                background-clip: text;
                color: transparent;
                text-shadow: none !important;
            }

            /* Hide decorative elements that don't print well */
            .floating-shapes, .shape, .poster-glow, .poster-overlay {
                display: none !important;
            }

            /* Optimize feature cards for print */
            .feature {
                /* Solid background color for better printing */
                background-color: rgba(26, 42, 108, 0.3) !important;
                background: linear-gradient(135deg,
                    rgba(26, 42, 108, 0.4) 0%,
                    rgba(26, 42, 108, 0.3) 100%) !important;
                border: 1.5px solid rgba(255, 255, 255, 0.4) !important;
                box-shadow: none !important;
                transform: none !important;
                transition: none !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .feature::after {
                display: none !important;
            }

            .feature h3 {
                color: #fdbb2d !important;
                text-shadow: none !important;
            }

            .feature p {
                color: #ffffff !important;
                text-shadow: none !important;
            }

            /* Optimize contact info for print */
            .contact-info, .header-contact-info {
                /* Solid background color for better printing */
                background-color: rgba(0, 0, 0, 0.4) !important;
                background: linear-gradient(135deg,
                    rgba(26, 42, 108, 0.5) 0%,
                    rgba(178, 31, 31, 0.4) 100%) !important;
                border: 1.5px solid rgba(178, 31, 31, 0.5) !important;
                box-shadow: none !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .contact-info p, .header-contact-info p {
                color: #ffffff !important;
                text-shadow: none !important;
            }

            .contact-info p i, .header-contact-info p i {
                color: #fdbb2d !important;
            }

            .capacity-note, .header-capacity-note {
                color: #fdbb2d !important;
                background-color: rgba(178, 31, 31, 0.3) !important;
                background: linear-gradient(90deg,
                    rgba(178, 31, 31, 0.4) 0%,
                    rgba(178, 31, 31, 0.3) 100%) !important;
                border: 1.5px solid rgba(178, 31, 31, 0.5) !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .qr-code::before, .header-qr-code::before {
                color: #ffffff !important;
            }

            .tagline {
                color: #fdbb2d !important;
                text-shadow: none !important;
            }

            /* Scale text appropriately for large format */
            html {
                font-size: 24pt; /* Base font size for large format */
            }

            .logo-text {
                font-size: 72pt;
                background: linear-gradient(to right, #b21f1f, #ffffff, #fdbb2d) !important;
                -webkit-background-clip: text !important;
                background-clip: text !important;
                color: transparent !important;
                text-shadow: none !important;
            }

            .tagline {
                font-size: 36pt;
            }

            .section-title {
                font-size: 48pt;
                animation: none !important;
            }

            .feature h3 {
                font-size: 30pt;
            }

            .feature p {
                font-size: 24pt;
            }

            .contact-info p, .header-contact-info p {
                font-size: 28pt;
            }

            .capacity-note, .header-capacity-note {
                font-size: 36pt;
            }

            /* Ensure QR code is large enough to scan */
            .qr-code, .header-qr-code {
                width: 300pt;
                height: 300pt;
                border: 3px solid #fdbb2d !important;
                background: #ffffff !important;
                box-shadow: none !important;
                animation: none !important;
            }

            .qr-code img, .header-qr-code img {
                width: 250pt;
                height: 250pt;
            }

            /* Remove all animations for print and ensure colors print properly */
            * {
                animation: none !important;
                transition: none !important;
                transform: none !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            /* Ensure header has proper background */
            header {
                background-color: rgba(26, 42, 108, 0.4) !important;
                background: linear-gradient(90deg,
                    rgba(26, 42, 108, 0.5) 0%,
                    rgba(178, 31, 31, 0.4) 50%,
                    rgba(253, 187, 45, 0.3) 100%) !important;
                border-bottom: 2px solid rgba(253, 187, 45, 0.6) !important;
            }
        }
    </style>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;600;700&family=Playfair+Display:wght@700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="poster-container">
        <div class="poster-glow"></div>
        <div class="poster-overlay"></div>

        <div class="floating-shapes">
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
        </div>

        <div class="container">
            <!-- Header Section - Spans full width -->
            <header class="fade-up">
                <div class="header-content">
                    <div class="header-left">
                        <!-- Contact Information -->
                        <div class="contact-info header-contact-info fade-up">
                            <p><i class="fas fa-map-marker-alt"></i> New Colony, Shrakwara, Khaitangan road</p>
                            <p><i class="fas fa-building"></i> Near KashurKulture Showroom, 193198</p>
                            <p><i class="fas fa-phone-alt"></i> +917789681275 | +918825063816</p>
                            <p><i class="fas fa-envelope"></i> <EMAIL></p>
                        </div>
                    </div>

                    <div class="header-center">
                        <div class="logo">
                            <div class="logo-container">
                                <div class="logo-glow"></div>
                                <img src="logo.png" alt="Hikmah Study Lounge" class="logo-img">
                                <div class="logo-text">Hikmah Study Lounge</div>
                            </div>
                        </div>
                        <div class="tagline">Your Gateway to Knowledge and Growth</div>
                    </div>

                    <div class="header-right">
                        <!-- QR Code -->
                        <div class="qr-code header-qr-code">
                            <img src="QRcode.png" alt="QR Code for Pre-Registration" width="100" height="100" style="border: 2px solid #fdbb2d; border-radius: 6px; padding: 4px; box-shadow: 0 0 15px rgba(253, 187, 45, 0.5);">
                        </div>
                        <div class="capacity-note header-capacity-note">
                            <i class="fas fa-user-friends"></i> Only 80 seats Capacity
                        </div>
                    </div>
                </div>
            </header>

            <!-- Sidebar - Left Column -->
            <div class="sidebar">
                <!-- Library Amenities Section -->
                <section class="fade-up" style="animation-delay: 0.2s;">
                    <h2 class="section-title"><i class="fas fa-book-open section-icon"></i>Library Amenities</h2>
                    <div class="features-grid">
                        <div class="feature">
                            <i class="fas fa-clock feature-icon"></i>
                            <div class="feature-content">
                                <h3>24/7 Access</h3>
                                <p>Our doors are always open to support your learning journey</p>
                            </div>
                        </div>
                        <div class="feature">
                            <i class="fas fa-couch feature-icon"></i>
                            <div class="feature-content">
                                <h3>Modern Furnishings</h3>
                                <p>Comfortable seating and ergonomic workspaces designed for long study sessions</p>
                            </div>
                        </div>
                        <div class="feature">
                            <i class="fas fa-leaf feature-icon"></i>
                            <div class="feature-content">
                                <h3>Peaceful Environment</h3>
                                <p>A serene, climate-controlled atmosphere perfect for focused study</p>
                            </div>
                        </div>
                        <div class="feature">
                            <i class="fas fa-wifi feature-icon"></i>
                            <div class="feature-content">
                                <h3>High-Speed Internet</h3>
                                <p>Complimentary WiFi access to support your research needs</p>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Additional Library Features Section -->
                <section class="fade-up" style="animation-delay: 1.0s;">
                    <h2 class="section-title"><i class="fas fa-plus-circle section-icon"></i>Additional Features</h2>
                    <div class="features-grid">
                        <div class="feature">
                            <i class="fas fa-shield-alt feature-icon"></i>
                            <div class="feature-content">
                                <h3>Safety & Security</h3>
                                <p>Round-the-clock CCTV surveillance and first aid facilities for your wellbeing</p>
                            </div>
                        </div>
                        <div class="feature">
                            <i class="fas fa-box feature-icon"></i>
                            <div class="feature-content">
                                <h3>Storage Solutions</h3>
                                <p>Secure lockers to store your belongings while you study</p>
                            </div>
                        </div>
                        <div class="feature">
                            <i class="fas fa-parking feature-icon"></i>
                            <div class="feature-content">
                                <h3>Convenient Parking</h3>
                                <p>Dedicated parking space for all library members</p>
                            </div>
                        </div>
                    </div>
                </section>
            </div>

            <!-- Main Content - Right Column -->
            <div class="main-content">
                <!-- Competitive Exam Preparation Hub Section -->
                <section class="fade-up" style="animation-delay: 0.4s;">
                    <h2 class="section-title"><i class="fas fa-graduation-cap section-icon"></i>Competitive Exam Preparation Hub</h2>
                    <div class="features-grid">
                        <div class="feature">
                            <i class="fas fa-chalkboard-teacher feature-icon"></i>
                            <div class="feature-content">
                                <h3>Expert-Led Sessions</h3>
                                <p>For JEE, NEET, UPSC, PSC, and JKSSB Aspirants (from IISc, IITs)</p>
                            </div>
                        </div>
                        <div class="feature">
                            <i class="fas fa-tasks feature-icon"></i>
                            <div class="feature-content">
                                <h3>Strategic Exam Preparation</h3>
                                <p>Master proven techniques for efficient study planning and time management</p>
                            </div>
                        </div>
                        <div class="feature">
                            <i class="fas fa-chart-line feature-icon"></i>
                            <div class="feature-content">
                                <h3>Performance Enhancement</h3>
                                <p>Learn advanced strategies for mock tests, managing exam pressure, and maximizing scores</p>
                            </div>
                        </div>
                        <div class="feature">
                            <i class="fas fa-newspaper feature-icon"></i>
                            <div class="feature-content">
                                <h3>Current Exam Insights</h3>
                                <p>Stay updated with the latest examination patterns and trending topics</p>
                            </div>
                        </div>
                        <div class="feature">
                            <i class="fas fa-users feature-icon"></i>
                            <div class="feature-content">
                                <h3>Peer Learning Community</h3>
                                <p>Connect with fellow aspirants for shared learning and motivation</p>
                            </div>
                        </div>
                        <div class="feature">
                            <i class="fas fa-book-reader feature-icon"></i>
                            <div class="feature-content">
                                <h3>Study Materials</h3>
                                <p>Access to comprehensive study materials and practice tests</p>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Career Guidance & Counselling Services Section -->
                <section class="fade-up" style="animation-delay: 0.6s;">
                    <h2 class="section-title"><i class="fas fa-briefcase section-icon"></i>Career Guidance & Counselling Services</h2>
                    <div class="features-grid">
                        <div class="feature">
                            <i class="fas fa-handshake feature-icon"></i>
                            <div class="feature-content">
                                <h3>Industry Connect</h3>
                                <p>Regular sessions with professionals from various fields for networking</p>
                            </div>
                        </div>
                        <div class="feature">
                            <i class="fas fa-clipboard-check feature-icon"></i>
                            <div class="feature-content">
                                <h3>Personalized Assessment</h3>
                                <p>Discover your strengths, interests, and aptitudes through expert guidance</p>
                            </div>
                        </div>
                        <div class="feature">
                            <i class="fas fa-compass feature-icon"></i>
                            <div class="feature-content">
                                <h3>Career Path Exploration</h3>
                                <p>Gain insights into diverse career options across traditional and emerging fields</p>
                            </div>
                        </div>
                        <div class="feature">
                            <i class="fas fa-map-marked-alt feature-icon"></i>
                            <div class="feature-content">
                                <h3>Custom Success Roadmap</h3>
                                <p>Develop a tailored career plan that aligns with your goals and aspirations</p>
                            </div>
                        </div>
                        <div class="feature">
                            <i class="fas fa-award feature-icon"></i>
                            <div class="feature-content">
                                <h3>Confidence Building</h3>
                                <p>Master interview preparation, resume writing, and personal branding</p>
                            </div>
                        </div>
                        <div class="feature">
                            <i class="fas fa-heart feature-icon"></i>
                            <div class="feature-content">
                                <h3>Anxiety Management</h3>
                                <p>Learn strategies to manage career-related stress through counselling support</p>
                            </div>
                        </div>
                        <div class="feature">
                            <i class="fas fa-book-reader feature-icon"></i>
                            <div class="feature-content">
                                <h3>Resource Access</h3>
                                <p>Utilize our collection of career guides and professional development tools</p>
                            </div>
                        </div>
                        <div class="feature">
                            <i class="fas fa-lightbulb feature-icon"></i>
                            <div class="feature-content">
                                <h3>Innovation Workshops</h3>
                                <p>Regular sessions on creative thinking and problem-solving for career growth</p>
                            </div>
                        </div>
                        <div class="feature">
                            <i class="fas fa-globe feature-icon"></i>
                            <div class="feature-content">
                                <h3>Global Opportunities</h3>
                                <p>Information on international education and career pathways</p>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </div>

    <script>
        // Modified script for print-friendly version
        document.addEventListener('DOMContentLoaded', function() {
            // Set all fade-up elements to be visible immediately
            const elements = document.querySelectorAll('.fade-up');
            elements.forEach(element => {
                element.style.opacity = '1';
                element.style.transform = 'translateY(0)';
            });

            // Add print-specific class to body when printing
            window.addEventListener('beforeprint', function() {
                document.body.classList.add('printing');

                // Make sure all animations are disabled
                document.querySelectorAll('.shape').forEach(shape => {
                    shape.style.animation = 'none';
                    shape.style.transform = 'none';
                });

                // Ensure all fade-up elements are fully visible
                document.querySelectorAll('.fade-up').forEach(el => {
                    el.style.opacity = '1';
                    el.style.transform = 'translateY(0)';
                    el.style.animation = 'none';
                });
            });

            // Only enable animations for screen viewing, not for print
            if (!window.matchMedia('print').matches) {
                // Simple scroll animation for screen viewing only
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            entry.target.style.opacity = '1';
                            entry.target.style.transform = 'translateY(0)';
                        }
                    });
                }, {
                    threshold: 0.1
                });

                elements.forEach(element => {
                    element.style.opacity = '0';
                    element.style.transform = 'translateY(20px)';
                    element.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
                    observer.observe(element);
                });

                // Add parallax effect to floating shapes - only for screen viewing
                window.addEventListener('mousemove', function(e) {
                    // Skip if we're in print mode
                    if (document.body.classList.contains('printing')) return;

                    const shapes = document.querySelectorAll('.shape');
                    const x = e.clientX / window.innerWidth;
                    const y = e.clientY / window.innerHeight;

                    shapes.forEach((shape, index) => {
                        const speed = 30 / (index + 1);
                        const xPos = (0.5 - x) * speed;
                        const yPos = (0.5 - y) * speed;

                        shape.style.transform = `translate(${xPos}px, ${yPos}px) rotate(${xPos * 0.5}deg)`;
                    });
                });
            }
        });
    </script>
</body>
</html>
