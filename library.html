<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hikmah Study Lounge - Educasheer</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&family=Montserrat:wght@400;500;600;700;800&family=Open+Sans:wght@400;500;600&family=Roboto+Mono:wght@400;500&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        @import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #1a2980 0%, #26d0ce 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            font-family: 'Inter', sans-serif;
            color: #333;
            position: relative;
            overflow-x: hidden;
        }

        /* Background Elements */
        .bg-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            opacity: 0.08;
            pointer-events: none;
        }

        .bg-element {
            position: absolute;
            color: white;
            font-size: 4rem;
            transition: all 10s ease-in-out;
            animation: float 15s infinite ease-in-out;
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
        }

        @keyframes float {
            0% { transform: translateY(0) rotate(0); }
            50% { transform: translateY(-15px) rotate(5deg); }
            100% { transform: translateY(0) rotate(0); }
        }

        .element-1 { top: 5%; left: 10%; font-size: 8rem; animation-delay: 0s; }
        .element-2 { top: 20%; right: 15%; animation-delay: 1s; }
        .element-3 { bottom: 15%; left: 5%; font-size: 6rem; animation-delay: 2s; }
        .element-4 { bottom: 25%; right: 10%; font-size: 5rem; animation-delay: 3s; }
        .element-5 { top: 50%; left: 50%; transform: translateX(-50%); font-size: 7rem; animation-delay: 4s; }
        .element-6 { top: 15%; left: 30%; font-size: 5rem; animation-delay: 5s; }
        .element-7 { top: 70%; right: 25%; font-size: 6rem; animation-delay: 6s; }
        .element-8 { top: 35%; left: 70%; font-size: 4.5rem; animation-delay: 7s; }
        .element-9 { top: 85%; left: 40%; font-size: 5.5rem; animation-delay: 8s; }
        .element-10 { top: 10%; right: 40%; font-size: 4rem; animation-delay: 9s; }

        .container {
            max-width: 1200px;
            width: 100%;
            background-color: rgba(255, 255, 255, 0.75);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border-radius: 24px;
            overflow: hidden;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2),
                        0 0 0 1px rgba(255, 255, 255, 0.2),
                        0 0 30px rgba(255, 255, 255, 0.1);
            position: relative;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        /* Admission Banner */
        .admission-banner {
            position: absolute;
            top: 40px;
            right: -35px;
            background: linear-gradient(135deg, #4568dc, #b06ab3);
            color: white;
            padding: 10px 40px;
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            font-size: 18px;
            transform: rotate(45deg);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2),
                        0 0 20px rgba(69, 104, 220, 0.3);
            z-index: 10;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        header {
            background: linear-gradient(135deg, #4568dc 0%, #b06ab3 100%);
            padding: 60px 30px 80px;
            text-align: center;
            position: relative;
            overflow: hidden;
            box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2) inset;
        }

        header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiPjxkZWZzPjxwYXR0ZXJuIGlkPSJwYXR0ZXJuIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgcGF0dGVyblVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgcGF0dGVyblRyYW5zZm9ybT0icm90YXRlKDQ1KSI+PHJlY3Qgd2lkdGg9IjEwIiBoZWlnaHQ9IjEwIiBmaWxsPSJyZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMDUpIi8+PC9wYXR0ZXJuPjwvZGVmcz48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI3BhdHRlcm4pIi8+PC9zdmc+');
            opacity: 0.5;
        }

        header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 40%;
            background: linear-gradient(to top, rgba(58, 28, 113, 0.4), transparent);
            z-index: 1;
        }

        .header-content {
            position: relative;
            z-index: 2;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logo {
            width: 250px;
            height: 150px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 30px;
            flex-shrink: 0;
            position: relative;
        }

        @keyframes logoGlow {
            0% { filter: drop-shadow(0 0 15px rgba(69, 104, 220, 0.7)); }
            50% { filter: drop-shadow(0 0 25px rgba(176, 106, 179, 0.8)); }
            100% { filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.6)); }
        }

        .logo img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            filter: drop-shadow(0 5px 15px rgba(0, 0, 0, 0.2));
            transition: transform 0.5s ease, filter 0.5s ease;
            animation: logoGlow 3s infinite alternate;
        }

        .logo:hover img {
            transform: scale(1.05);
        }

        .title-container {
            text-align: left;
        }

        header h1 {
            font-family: 'Poppins', sans-serif;
            color: white;
            font-size: 52px;
            font-weight: 800;
            letter-spacing: -0.5px;
            margin-bottom: 10px;
            position: relative;
            text-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            line-height: 1.1;
            background: linear-gradient(to right, #ffffff, #e0d6ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        header h2 {
            font-family: 'Montserrat', sans-serif;
            color: rgba(255, 255, 255, 0.95);
            font-size: 22px;
            font-weight: 500;
            letter-spacing: 2px;
            text-transform: uppercase;
            position: relative;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
            padding-bottom: 5px;
        }

        header h2::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 3px;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 3px;
        }

        .intro {
            padding: 40px;
            text-align: center;
            background: rgba(248, 249, 250, 0.7);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05) inset,
                        0 0 30px rgba(69, 104, 220, 0.1);
        }

        .intro::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiPjxkZWZzPjxwYXR0ZXJuIGlkPSJwYXR0ZXJuIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgcGF0dGVyblVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgcGF0dGVyblRyYW5zZm9ybT0icm90YXRlKDQ1KSI+PHJlY3Qgd2lkdGg9IjEwIiBoZWlnaHQ9IjEwIiBmaWxsPSJyZ2JhKDAsIDAsIDAsIDAuMDIpIi8+PC9wYXR0ZXJuPjwvZGVmcz48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI3BhdHRlcm4pIi8+PC9zdmc+');
            opacity: 0.5;
            z-index: -1;
        }

        .intro h3 {
            font-family: 'Montserrat', sans-serif;
            color: #333;
            font-size: 22px;
            line-height: 1.6;
            margin-bottom: 20px;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .intro p {
            font-size: 18px;
            line-height: 1.8;
            color: #505050;
            max-width: 800px;
            margin: 0 auto;
            text-shadow: 0 1px 1px rgba(0, 0, 0, 0.02);
        }

        .highlight {
            color: #4568dc;
            font-weight: 600;
            background: linear-gradient(90deg, #4568dc, #b06ab3);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 2px 10px rgba(69, 104, 220, 0.2);
        }

        .programs {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(340px, 1fr));
            gap: 30px;
            padding: 40px;
            background: linear-gradient(to bottom, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.95));
            box-shadow: 0 0 30px rgba(69, 104, 220, 0.05) inset;
        }

        .program {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08),
                        0 0 0 1px rgba(255, 255, 255, 0.1),
                        0 0 20px rgba(69, 104, 220, 0.1);
            transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .program:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12),
                        0 0 0 1px rgba(255, 255, 255, 0.2),
                        0 0 30px rgba(69, 104, 220, 0.2);
        }

        .program-header {
            position: relative;
            padding: 24px 20px;
            display: flex;
            align-items: center;
        }

        /* Card header gradients for the three main sections */
        .library-amenities .program-header { background: linear-gradient(135deg, #12c2e9 0%, #c471ed 50%, #f64f59 100%); }
        .exam-prep .program-header { background: linear-gradient(135deg, #8E2DE2 0%, #4A00E0 100%); }
        .career-guidance .program-header { background: linear-gradient(135deg, #0cebeb 0%, #20e3b2 50%, #29ffc6 100%); }

        .program-number {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: 'Montserrat', sans-serif;
            font-size: 24px;
            font-weight: 700;
            color: #333;
            margin-right: 16px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1),
                        0 0 0 1px rgba(255, 255, 255, 0.3),
                        0 0 10px rgba(255, 255, 255, 0.5);
            border: 1px solid rgba(255, 255, 255, 0.5);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .program-number i {
            font-size: 28px;
            background: linear-gradient(135deg, #4568dc, #b06ab3);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
        }

        .program:hover .program-number {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15),
                        0 0 0 1px rgba(255, 255, 255, 0.5),
                        0 0 20px rgba(255, 255, 255, 0.7);
        }

        .program-title {
            font-family: 'Montserrat', sans-serif;
            font-size: 22px;
            font-weight: 700;
            color: white;
            letter-spacing: 0.5px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            position: relative;
            padding-bottom: 5px;
        }

        .program-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 40px;
            height: 2px;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 2px;
            transition: width 0.3s ease;
        }

        .program:hover .program-title::after {
            width: 80px;
        }

        .program-content {
            padding: 30px 25px;
        }

        .program-content ul {
            list-style-type: none;
            padding: 0;
            margin: 0 0 15px 0;
        }

        .program-content li {
            color: #505050;
            line-height: 1.7;
            margin-bottom: 12px;
            font-size: 16px;
            position: relative;
            padding-left: 28px;
            display: flex;
            align-items: flex-start;
        }

        .program-content li:before {
            content: "✓";
            color: #4568dc;
            font-weight: bold;
            position: absolute;
            left: 0;
            top: 0;
            font-size: 18px;
            text-shadow: 0 0 5px rgba(69, 104, 220, 0.3);
        }

        .key-benefit {
            background: rgba(69, 104, 220, 0.08);
            border-left: 3px solid #4568dc;
            padding: 12px 15px;
            margin-top: 15px;
            border-radius: 0 4px 4px 0;
            font-weight: 500;
            color: #333;
            font-size: 15px;
            display: flex;
            align-items: center;
            box-shadow: 0 3px 10px rgba(69, 104, 220, 0.1);
        }

        .key-benefit:before {
            content: "\f084";
            font-family: "Font Awesome 5 Free";
            font-weight: 900;
            margin-right: 10px;
            font-size: 18px;
            color: #4568dc;
        }

        .highlight-text {
            color: #4568dc;
            font-weight: 600;
            text-shadow: 0 0 1px rgba(69, 104, 220, 0.3);
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin: 30px 0;
        }

        .feature {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(12px);
            border-radius: 15px;
            padding: 22px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.15);
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
            display: flex;
            align-items: flex-start;
        }

        .feature::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                rgba(255, 255, 255, 0) 0%,
                rgba(255, 255, 255, 0.05) 50%,
                rgba(255, 255, 255, 0) 100%);
            transition: all 0.5s ease;
        }

        .feature:hover::after {
            left: 100%;
        }

        .feature:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.25), 0 0 15px rgba(178, 31, 31, 0.3);
            background: rgba(255, 255, 255, 0.12);
            border: 1px solid rgba(253, 187, 45, 0.5);
        }

        .feature-icon {
            font-size: 24px;
            background: linear-gradient(135deg, #fdbb2d, #b21f1f);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            margin-right: 15px;
            margin-top: 3px;
            text-shadow: 0 0 10px rgba(253, 187, 45, 0.5);
        }

        .feature-content {
            flex: 1;
        }

        .feature h3 {
            font-size: 20px;
            margin-bottom: 12px;
            color: #fdbb2d;
            font-weight: 600;
            letter-spacing: 0.5px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .feature p {
            font-size: 16px;
            line-height: 1.6;
            color: rgba(255, 255, 255, 0.9);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        footer {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            padding: 40px;
            background: rgba(248, 249, 250, 0.7);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            color: #505050;
            font-size: 15px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiPjxkZWZzPjxwYXR0ZXJuIGlkPSJwYXR0ZXJuIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgcGF0dGVyblVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgcGF0dGVyblRyYW5zZm9ybT0icm90YXRlKDQ1KSI+PHJlY3Qgd2lkdGg9IjEwIiBoZWlnaHQ9IjEwIiBmaWxsPSJyZ2JhKDAsIDAsIDAsIDAuMDIpIi8+PC9wYXR0ZXJuPjwvZGVmcz48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI3BhdHRlcm4pIi8+PC9zdmc+');
            opacity: 0.5;
            z-index: -1;
        }

        .footer-col {
            display: flex;
            flex-direction: column;
            background: rgba(255, 255, 255, 0.5);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05),
                        0 0 0 1px rgba(255, 255, 255, 0.2),
                        0 0 20px rgba(69, 104, 220, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .footer-col:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08),
                        0 0 0 1px rgba(255, 255, 255, 0.3),
                        0 0 30px rgba(69, 104, 220, 0.2);
        }

        .footer-heading {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            font-size: 18px;
            margin-bottom: 15px;
            color: #333;
            position: relative;
            padding-bottom: 10px;
            display: flex;
            align-items: center;
        }

        .footer-heading i {
            color: #4568dc;
            text-shadow: 0 0 5px rgba(69, 104, 220, 0.3);
        }

        .footer-heading::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 40px;
            height: 3px;
            background: linear-gradient(90deg, #4568dc, #b06ab3);
            border-radius: 3px;
        }

        .contact-info p {
            margin-bottom: 10px;
            display: flex;
            align-items: flex-start;
            color: #505050;
        }

        .contact-info i {
            margin-right: 10px;
            color: #4568dc;
            font-size: 18px;
            line-height: 1.5;
            width: 20px;
            text-align: center;
            text-shadow: 0 0 5px rgba(69, 104, 220, 0.3);
        }

        .capacity-note {
            font-size: 18px;
            font-weight: 600;
            color: #4568dc;
            margin-top: 15px;
            letter-spacing: 0.5px;
            display: flex;
            align-items: center;
            background: rgba(69, 104, 220, 0.08);
            padding: 12px;
            border-radius: 8px;
            border: 1px solid rgba(69, 104, 220, 0.2);
            box-shadow: 0 3px 10px rgba(69, 104, 220, 0.1);
        }

        .capacity-note i {
            margin-right: 10px;
            color: #4568dc;
        }

        .qr-code {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .qr-code-img {
            width: 150px;
            height: 150px;
            margin-bottom: 10px;
            background: white;
            border: 1px solid rgba(255, 255, 255, 0.5);
            border-radius: 12px;
            padding: 10px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08),
                        0 0 0 1px rgba(255, 255, 255, 0.3),
                        0 0 20px rgba(69, 104, 220, 0.15);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .qr-code-img:hover {
            transform: scale(1.05);
            box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12),
                        0 0 0 1px rgba(255, 255, 255, 0.3),
                        0 0 30px rgba(69, 104, 220, 0.3);
        }

        .copyright {
            grid-column: 1 / -1;
            text-align: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            color: #666;
            font-size: 14px;
            font-weight: 500;
        }

        /* Glass morphism elements */
        .glass {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(12px);
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(178, 31, 31, 0.2);
            margin: 30px 0;
            transition: all 0.3s ease;
        }

        .glass:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
        }

        /* Animations */
        @keyframes fadeUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-up {
            animation: fadeUp 0.8s ease forwards;
        }

        /* Additional decorative elements */
        .floating-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            overflow: hidden;
        }

        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(5px);
            border-radius: 50%;
            animation: float 20s infinite ease-in-out;
            box-shadow: inset 0 0 30px rgba(178, 31, 31, 0.15);
            border: 1px solid rgba(253, 187, 45, 0.1);
        }

        .shape:nth-child(1) {
            width: 200px;
            height: 200px;
            top: 10%;
            left: 10%;
            animation-delay: 0s;
            background: radial-gradient(circle at 30% 40%, rgba(178, 31, 31, 0.08), rgba(178, 31, 31, 0.01));
        }

        .shape:nth-child(2) {
            width: 300px;
            height: 300px;
            top: 60%;
            left: 5%;
            animation-delay: 3s;
            background: radial-gradient(circle at 70% 20%, rgba(253, 187, 45, 0.08), rgba(253, 187, 45, 0.01));
        }

        .shape:nth-child(3) {
            width: 250px;
            height: 250px;
            top: 20%;
            right: 5%;
            animation-delay: 6s;
            background: radial-gradient(circle at 40% 50%, rgba(178, 31, 31, 0.08), rgba(178, 31, 31, 0.01));
        }

        .shape:nth-child(4) {
            width: 150px;
            height: 150px;
            top: 30%;
            right: 20%;
            animation-delay: 9s;
            background: radial-gradient(circle at 60% 30%, rgba(253, 187, 45, 0.08), rgba(253, 187, 45, 0.01));
        }

        @keyframes float {
            0% {
                transform: translate(0, 0) rotate(0deg);
                opacity: 0.7;
            }
            25% {
                transform: translate(15px, 25px) rotate(5deg);
                opacity: 0.8;
            }
            50% {
                transform: translate(25px, 45px) rotate(10deg);
                opacity: 0.9;
            }
            75% {
                transform: translate(15px, 25px) rotate(5deg);
                opacity: 0.8;
            }
            100% {
                transform: translate(0, 0) rotate(0deg);
                opacity: 0.7;
            }
        }

        /* Print button styles */
        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #4568dc 0%, #b06ab3 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 50px;
            cursor: pointer;
            font-family: 'Montserrat', sans-serif;
            font-weight: 600;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2),
                        0 0 0 1px rgba(255, 255, 255, 0.1),
                        0 0 20px rgba(69, 104, 220, 0.3);
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            font-size: 15px;
            letter-spacing: 0.5px;
            text-decoration: none;
        }

        .print-button:hover {
            background: linear-gradient(135deg, #5B86E5 0%, #b06ab3 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25),
                        0 0 0 1px rgba(255, 255, 255, 0.2),
                        0 0 30px rgba(69, 104, 220, 0.4);
        }

        /* Responsive styles */
        @media (max-width: 992px) {
            .header-content {
                flex-direction: column;
                text-align: center;
            }

            .logo {
                margin-right: 0;
                margin-bottom: 20px;
            }

            .title-container {
                text-align: center;
            }

            header h1 {
                font-size: 40px;
            }

            .admission-banner {
                transform: rotate(0);
                right: 0;
                top: 0;
                width: 100%;
                text-align: center;
            }
        }

        /* Amenities Grid Styles */
        .amenities-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 25px;
        }

        .amenity-item {
            display: flex;
            align-items: flex-start;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 12px;
            padding: 15px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .amenity-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
            background: rgba(255, 255, 255, 0.9);
        }

        .amenity-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #12c2e9 0%, #c471ed 100%);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 12px;
            color: white;
            font-size: 18px;
            flex-shrink: 0;
            box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
        }

        .amenity-text h4 {
            font-family: 'Montserrat', sans-serif;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 5px;
            color: #333;
        }

        .amenity-text p {
            font-size: 14px;
            color: #505050;
            line-height: 1.4;
        }

        /* Exam Prep Features Styles */
        .prep-features {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 25px;
            margin-bottom: 25px;
        }

        .prep-feature {
            display: flex;
            align-items: flex-start;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .prep-feature:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
            background: rgba(255, 255, 255, 0.9);
        }

        .prep-icon {
            width: 45px;
            height: 45px;
            background: linear-gradient(135deg, #8E2DE2 0%, #4A00E0 100%);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 15px;
            color: white;
            font-size: 20px;
            flex-shrink: 0;
            box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
        }

        .prep-content h4 {
            font-family: 'Montserrat', sans-serif;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #333;
        }

        .prep-content p {
            font-size: 15px;
            color: #505050;
            line-height: 1.5;
        }

        /* Career Features Styles */
        .career-features {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 25px;
            margin-bottom: 25px;
        }

        .career-feature {
            display: flex;
            align-items: flex-start;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .career-feature:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
            background: rgba(255, 255, 255, 0.9);
        }

        .career-icon {
            width: 45px;
            height: 45px;
            background: linear-gradient(135deg, #0cebeb 0%, #20e3b2 100%);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 15px;
            color: white;
            font-size: 20px;
            flex-shrink: 0;
            box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
        }

        .career-content h4 {
            font-family: 'Montserrat', sans-serif;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #333;
        }

        .career-content p {
            font-size: 15px;
            color: #505050;
            line-height: 1.5;
        }

        /* Enhanced program styles */
        .program.library-amenities,
        .program.exam-prep,
        .program.career-guidance {
            min-height: 500px;
            display: flex;
            flex-direction: column;
        }

        .program.library-amenities .program-content,
        .program.exam-prep .program-content,
        .program.career-guidance .program-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .program.library-amenities .key-benefit,
        .program.exam-prep .key-benefit,
        .program.career-guidance .key-benefit {
            margin-top: auto;
            font-size: 16px;
            padding: 15px;
            text-align: center;
            background: rgba(69, 104, 220, 0.08);
            border-radius: 8px;
        }

        @media (max-width: 768px) {
            header {
                padding: 60px 20px 30px;
            }

            header h1 {
                font-size: 32px;
            }

            header h2 {
                font-size: 18px;
            }

            .intro {
                padding: 30px 20px;
            }

            .intro h3 {
                font-size: 18px;
            }

            .intro p {
                font-size: 16px;
            }

            .programs {
                padding: 20px;
                gap: 20px;
            }

            footer {
                padding: 30px 20px;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }

            .feature h3 {
                font-size: 18px;
            }

            .feature p {
                font-size: 14px;
            }

            /* Responsive adjustments for new grids */
            .amenities-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .prep-features,
            .career-features {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 576px) {
            .amenities-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Print styles */
        @media print {
            body {
                background: #fff;
                color: #000;
            }

            .container {
                box-shadow: none;
                border: none;
            }

            .bg-elements, .print-button {
                display: none;
            }

            header {
                background: #f5f5f7;
            }

            header h1 {
                color: #333;
                -webkit-text-fill-color: #333;
            }

            header h2 {
                color: #555;
            }

            .program-header {
                background: #f0f0f0 !important;
            }

            .program-title {
                color: #333;
            }

            .program-title::after {
                background: #333;
            }

            .footer-col {
                background: #f5f5f7;
                box-shadow: none;
            }

            .footer-heading i, .contact-info i {
                color: #333;
            }

            .copyright {
                color: #555;
            }

            /* Print styles for new grid layouts */
            .amenities-grid,
            .prep-features,
            .career-features {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }

            .amenity-icon,
            .prep-icon,
            .career-icon {
                background: #f0f0f0 !important;
                color: #333 !important;
                box-shadow: none;
            }

            .program-number i {
                -webkit-text-fill-color: #333;
                filter: none;
            }

            .amenity-item,
            .prep-feature,
            .career-feature {
                background: #f8f8f8;
                box-shadow: none;
                border: 1px solid #eee;
            }

            .key-benefit {
                background: #f0f0f0;
                border: 1px solid #ddd;
            }
        }
    </style>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;600;700&family=Playfair+Display:wght@700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Print Button -->
    <a href="#" onclick="window.print(); return false;" class="print-button">
        <i class="fas fa-print"></i> Print Version
    </a>

    <!-- Background Elements -->
    <div class="bg-elements">
        <div class="bg-element element-1"><i class="fas fa-book"></i></div>
        <div class="bg-element element-2"><i class="fas fa-graduation-cap"></i></div>
        <div class="bg-element element-3"><i class="fas fa-pencil-alt"></i></div>
        <div class="bg-element element-4"><i class="fas fa-chalkboard-teacher"></i></div>
        <div class="bg-element element-5"><i class="fas fa-university"></i></div>
        <div class="bg-element element-6"><i class="fas fa-atom"></i></div>
        <div class="bg-element element-7"><i class="fas fa-calculator"></i></div>
        <div class="bg-element element-8"><i class="fas fa-microscope"></i></div>
        <div class="bg-element element-9"><i class="fas fa-flask"></i></div>
        <div class="bg-element element-10"><i class="fas fa-brain"></i></div>
    </div>

    <div class="container">
        <div class="admission-banner">ADMISSIONS OPEN</div>

        <header>
            <div class="header-content">
                <div class="logo">
                    <img src="logo.png" alt="Hikmah Study Lounge Logo">
                </div>
                <div class="title-container">
                    <h1>Hikmah Study Lounge</h1>
                    <h2>Your Gateway to Knowledge and Growth</h2>
                </div>
            </div>
        </header>

        <section class="intro">
            <h3><i class="fas fa-rocket" style="margin-right: 10px; color: #4568dc; text-shadow: 0 0 10px rgba(69, 104, 220, 0.4);"></i>Join our premier study lounge led by <span class="highlight">expert educators</span> with a passion for academic excellence.</h3>
            <p>Whether you're preparing for competitive exams or seeking a peaceful study environment, our comprehensive facilities will empower your educational journey!</p>
        </section>

        <section class="programs">
            <div class="program library-amenities">
                <div class="program-header">
                    <div class="program-number"><i class="fas fa-book-reader"></i></div>
                    <div class="program-title">Library Amenities</div>
                </div>
                <div class="program-content">
                    <div class="amenities-grid">
                        <div class="amenity-item">
                            <div class="amenity-icon"><i class="fas fa-clock"></i></div>
                            <div class="amenity-text">
                                <h4>24/7 Access</h4>
                                <p>Always open with with online attendance</p>
                            </div>
                        </div>
                        <div class="amenity-item">
                            <div class="amenity-icon"><i class="fas fa-couch"></i></div>
                            <div class="amenity-text">
                                <h4>Modern Facilities</h4>
                                <p>Ergonomic workspaces & comfortable seating</p>
                            </div>
                        </div>
                        <div class="amenity-item">
                            <div class="amenity-icon"><i class="fas fa-wifi"></i></div>
                            <div class="amenity-text">
                                <h4>Tech Support</h4>
                                <p>High-speed internet & Access to planner platforms</p>
                            </div>
                        </div>
                        <div class="amenity-item">
                            <div class="amenity-icon"><i class="fas fa-shield-alt"></i></div>
                            <div class="amenity-text">
                                <h4>Safety & Security</h4>
                                <p>24/7 CCTV surveillance & secure lockers</p>
                            </div>
                        </div>
                        <div class="amenity-item">
                            <div class="amenity-icon"><i class="fas fa-book"></i></div>
                            <div class="amenity-text">
                                <h4>Study Resources</h4>
                                <p>Comprehensive reference materials & databases</p>
                            </div>
                        </div>
                        <div class="amenity-item">
                            <div class="amenity-icon"><i class="fas fa-coffee"></i></div>
                            <div class="amenity-text">
                                <h4>Refreshment Zone</h4>
                                <p>Coffee, tea, and snacks available</p>
                            </div>
                        </div>
                    </div>
                    <div class="key-benefit">A complete ecosystem designed for academic excellence</div>
                </div>
            </div>
        </section>

        <section class="intro">
            <h3><i class="fas fa-graduation-cap" style="margin-right: 10px; color: #4568dc; text-shadow: 0 0 10px rgba(69, 104, 220, 0.4);"></i>Competitive Exam Preparation Hub</h3>
            <p>Our specialized programs are designed to help you excel in competitive exams with expert guidance, strategic preparation, and comprehensive resources.</p>
        </section>

        <section class="programs">
            <div class="program exam-prep">
                <div class="program-header">
                    <div class="program-number"><i class="fas fa-graduation-cap"></i></div>
                    <div class="program-title">Competitive Exam Preparation Hub</div>
                </div>
                <div class="program-content">
                    <div class="prep-features">
                        <div class="prep-feature">
                            <div class="prep-icon"><i class="fas fa-chalkboard-teacher"></i></div>
                            <div class="prep-content">
                                <h4>Expert-Led Sessions</h4>
                                <p>Guidance from <span class="highlight-text">IISc and IIT graduates and other experts</span> with specialized guidance for JEE, NEET, UPSC, PSC, and JKSSB exams</p>
                            </div>
                        </div>
                        <div class="prep-feature">
                            <div class="prep-icon"><i class="fas fa-tasks"></i></div>
                            <div class="prep-content">
                                <h4>Strategic Preparation</h4>
                                <p><span class="highlight-text">Customized study plans</span> with efficient time management techniques and structured syllabus coverage plan, with online tools</p>
                            </div>
                        </div>
                        <div class="prep-feature">
                            <div class="prep-icon"><i class="fas fa-chart-line"></i></div>
                            <div class="prep-content">
                                <h4>Performance Enhancement</h4>
                                <p><span class="highlight-text">Regular mock tests</span> with detailed analysis and strategies to improve performance</p>
                            </div>
                        </div>
                        <div class="prep-feature">
                            <div class="prep-icon"><i class="fas fa-newspaper"></i></div>
                            <div class="prep-content">
                                <h4>Current Exam Insights</h4>
                                <p>Stay updated with <span class="highlight-text">latest examination patterns</span> and focus on high-probability topics</p>
                            </div>
                        </div>
                    </div>
                    <div class="key-benefit">Comprehensive preparation strategy for competitive success</div>
                </div>
            </div>
        </section>

        <section class="intro">
            <h3><i class="fas fa-briefcase" style="margin-right: 10px; color: #4568dc; text-shadow: 0 0 10px rgba(69, 104, 220, 0.4);"></i>Career Guidance & Counselling Services</h3>
            <p>Navigate your career path with confidence through our comprehensive guidance services designed to help you make informed decisions about your future.</p>
        </section>

        <section class="programs">
            <div class="program career-guidance">
                <div class="program-header">
                    <div class="program-number"><i class="fas fa-briefcase"></i></div>
                    <div class="program-title">Career Guidance & Counselling Services</div>
                </div>
                <div class="program-content">
                    <div class="career-features">
                        <div class="career-feature">
                            <div class="career-icon"><i class="fas fa-handshake"></i></div>
                            <div class="career-content">
                                <h4>Expert Connect</h4>
                                <p>Regular sessions with <span class="highlight-text">professionals</span> to gain insights and better understanding of various career paths</p>
                            </div>
                        </div>
                        <div class="career-feature">
                            <div class="career-icon"><i class="fas fa-clipboard-check"></i></div>
                            <div class="career-content">
                                <h4>Career Assessment</h4>
                                <p><span class="highlight-text">Personalized evaluation</span> of strengths with scientific assessment tools and one-on-one counseling</p>
                            </div>
                        </div>
                        <div class="career-feature">
                            <div class="career-icon"><i class="fas fa-award"></i></div>
                            <div class="career-content">
                                <h4>Skill Development</h4>
                                <p><span class="highlight-text">Interview preparation</span>, resume building, and communication skills training</p>
                            </div>
                        </div>
                        <div class="career-feature">
                            <div class="career-icon"><i class="fas fa-heart"></i></div>
                            <div class="career-content">
                                <h4>Emotional Support</h4>
                                <p>Strategies to manage <span class="highlight-text">career-related stress</span> and build resilience for competitive environments</p>
                            </div>
                        </div>
                    </div>
                    <div class="key-benefit">Holistic career development for long-term professional success</div>
                </div>
            </div>
        </section>

        <footer>
            <div class="footer-col contact-info">
                <h3 class="footer-heading"><i class="fas fa-address-book" style="margin-right: 8px;"></i>Contact Us</h3>
                <p><i class="fas fa-phone-alt"></i> +91 7789681275 | +91 8825063816</p>
                <p><i class="fas fa-envelope"></i> <EMAIL></p>

                <div class="capacity-note">
                    <i class="fas fa-user-friends"></i> Only 80 seats available. First come, first served.
                </div>
            </div>

            <div class="footer-col contact-info">
                <h3 class="footer-heading"><i class="fas fa-map-marked-alt" style="margin-right: 8px;"></i>Address</h3>
                <p><i class="fas fa-map-marker-alt"></i> Hikmah Study Lounge<br>
                New Colony, Shrakwara, Khaitangan road<br>
                Near KashurKulture Showroom, 193198</p>
            </div>

            <div class="footer-col qr-code">
                <h3 class="footer-heading"><i class="fas fa-qrcode" style="margin-right: 8px;"></i>Scan for Pre-Registration</h3>
                <div class="qr-code-img">
                    <img src="QRcode.png" alt="QR Code" width="130" height="130" style="border: 3px solid #4568dc; border-radius: 8px; padding: 5px; box-shadow: 0 0 15px rgba(69, 104, 220, 0.4);">
                </div>
                <p>Scan to pre-register and secure your spot</p>
            </div>

            <div class="copyright">
                <p>© 2025 Hikmah Study Lounge. All Rights Reserved.</p>
            </div>
        </footer>
    </div>

    <script>
        // Add parallax effect to background elements
        document.addEventListener('DOMContentLoaded', function() {
            // Parallax effect for background elements
            window.addEventListener('mousemove', function(e) {
                const elements = document.querySelectorAll('.bg-element');
                const x = e.clientX / window.innerWidth;
                const y = e.clientY / window.innerHeight;

                elements.forEach((element, index) => {
                    const speed = 20 / (index + 1);
                    const xPos = (0.5 - x) * speed;
                    const yPos = (0.5 - y) * speed;

                    element.style.transform = `translate(${xPos}px, ${yPos}px) rotate(${xPos * 0.5}deg)`;
                });
            });

            // Smooth scroll for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    if (this.getAttribute('href') !== '#') {
                        e.preventDefault();
                        document.querySelector(this.getAttribute('href')).scrollIntoView({
                            behavior: 'smooth'
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>
