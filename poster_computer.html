<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EduCasheer SiliconSpark</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&family=Montserrat:wght@400;500;600;700;800&family=Open+Sans:wght@400;500;600&family=Roboto+Mono:wght@400;500&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        @import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #1a2980 0%, #26d0ce 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            font-family: 'Inter', sans-serif;
            color: #333;
            position: relative;
            overflow-x: hidden;
        }

        /* Set landscape orientation and specific size for 6ft × 3ft (72in × 36in) */
        @page {
            size: 72in 36in landscape;
        }

        @media print {
            body {
                width: 72in;
                height: 36in;
                margin: 0;
                padding: 0;
            }

            .container {
                width: 100%;
                height: 100%;
                max-width: none;
                margin: 0;
                border-radius: 0;
            }
        }

        /* Background Tech Elements */
        .bg-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            opacity: 0.08;
            pointer-events: none;
        }

        .bg-element {
            position: absolute;
            color: white;
            font-size: 4rem;
            transition: all 10s ease-in-out;
            animation: float 15s infinite ease-in-out;
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
        }

        @keyframes float {
            0% { transform: translateY(0) rotate(0); }
            50% { transform: translateY(-15px) rotate(5deg); }
            100% { transform: translateY(0) rotate(0); }
        }

        .element-1 { top: 5%; left: 10%; font-size: 8rem; animation-delay: 0s; }
        .element-2 { top: 20%; right: 15%; animation-delay: 1s; }
        .element-3 { bottom: 15%; left: 5%; font-size: 6rem; animation-delay: 2s; }
        .element-4 { bottom: 25%; right: 10%; font-size: 5rem; animation-delay: 3s; }
        .element-5 { top: 50%; left: 50%; transform: translateX(-50%); font-size: 7rem; animation-delay: 4s; }
        .element-6 { top: 15%; left: 30%; font-size: 5rem; animation-delay: 5s; }
        .element-7 { top: 70%; right: 25%; font-size: 6rem; animation-delay: 6s; }
        .element-8 { top: 35%; left: 70%; font-size: 4.5rem; animation-delay: 7s; }
        .element-9 { top: 85%; left: 40%; font-size: 5.5rem; animation-delay: 8s; }
        .element-10 { top: 10%; right: 40%; font-size: 4rem; animation-delay: 9s; }

        .container {
            max-width: 1800px; /* Optimized for 6ft width */
            width: 100%;
            background-color: rgba(255, 255, 255, 0.75);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border-radius: 24px;
            overflow: hidden;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2),
                        0 0 0 1px rgba(255, 255, 255, 0.2),
                        0 0 30px rgba(255, 255, 255, 0.1);
            position: relative;
            border: 1px solid rgba(255, 255, 255, 0.3);
            display: flex;
            flex-direction: column;
            height: 95vh; /* Control height for landscape */
            aspect-ratio: 2 / 1; /* 6ft × 3ft aspect ratio */
        }

        /* Admission Banner */
        .admission-banner {
            position: absolute;
            top: 40px;
            right: -35px;
            background: linear-gradient(135deg, #4568dc, #b06ab3);
            color: white;
            padding: 10px 40px;
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            font-size: 18px;
            transform: rotate(45deg);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2),
                        0 0 20px rgba(69, 104, 220, 0.3);
            z-index: 10;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        header {
            background: linear-gradient(135deg, #4568dc 0%, #b06ab3 100%);
            padding: 40px 50px 50px;
            text-align: center;
            position: relative;
            overflow: hidden;
            box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2) inset;
            flex-shrink: 0;
            height: 22%; /* Proportional for 6ft × 3ft */
        }

        header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiPjxkZWZzPjxwYXR0ZXJuIGlkPSJwYXR0ZXJuIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgcGF0dGVyblVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgcGF0dGVyblRyYW5zZm9ybT0icm90YXRlKDQ1KSI+PHJlY3Qgd2lkdGg9IjEwIiBoZWlnaHQ9IjEwIiBmaWxsPSJyZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMDUpIi8+PC9wYXR0ZXJuPjwvZGVmcz48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI3BhdHRlcm4pIi8+PC9zdmc+');
            opacity: 0.5;
        }

        header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 40%;
            background: linear-gradient(to top, rgba(58, 28, 113, 0.4), transparent);
            z-index: 1;
        }

        .header-content {
            position: relative;
            z-index: 2;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            padding-left: 40px;
        }

        .logo {
            width: 240px;
            height: 160px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 40px;
            flex-shrink: 0;
            position: relative;
        }

        @keyframes logoGlow {
            0% { filter: drop-shadow(0 0 15px rgba(69, 104, 220, 0.7)); }
            50% { filter: drop-shadow(0 0 25px rgba(176, 106, 179, 0.8)); }
            100% { filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.6)); }
        }

        .logo img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            filter: drop-shadow(0 5px 15px rgba(0, 0, 0, 0.2));
            transition: transform 0.5s ease, filter 0.5s ease;
            animation: logoGlow 3s infinite alternate;
        }

        .logo:hover img {
            transform: scale(1.05);
        }

        .title-container {
            text-align: left;
        }

        header h1 {
            font-family: 'Poppins', sans-serif;
            color: white;
            font-size: 72px;
            font-weight: 800;
            letter-spacing: -0.5px;
            margin-bottom: 10px;
            position: relative;
            text-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            line-height: 1.1;
            background: linear-gradient(to right, #ffffff, #e0d6ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        header h2 {
            font-family: 'Montserrat', sans-serif;
            color: rgba(255, 255, 255, 0.95);
            font-size: 32px;
            font-weight: 500;
            letter-spacing: 3px;
            text-transform: uppercase;
            position: relative;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
            padding-bottom: 8px;
        }

        header h2::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 3px;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 3px;
        }

        .intro {
            padding: 25px 60px;
            text-align: center;
            background: rgba(248, 249, 250, 0.7);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05) inset,
                        0 0 30px rgba(69, 104, 220, 0.1);
            flex-shrink: 0;
            height: 13%; /* Proportional for 6ft × 3ft */
        }

        .intro::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiPjxkZWZzPjxwYXR0ZXJuIGlkPSJwYXR0ZXJuIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgcGF0dGVyblVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgcGF0dGVyblRyYW5zZm9ybT0icm90YXRlKDQ1KSI+PHJlY3Qgd2lkdGg9IjEwIiBoZWlnaHQ9IjEwIiBmaWxsPSJyZ2JhKDAsIDAsIDAsIDAuMDIpIi8+PC9wYXR0ZXJuPjwvZGVmcz48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI3BhdHRlcm4pIi8+PC9zdmc+');
            opacity: 0.5;
            z-index: -1;
        }

        .intro h3 {
            font-family: 'Montserrat', sans-serif;
            color: #333;
            font-size: 28px;
            line-height: 1.5;
            margin-bottom: 15px;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .intro p {
            font-size: 22px;
            line-height: 1.6;
            color: #505050;
            max-width: 1600px;
            margin: 0 auto;
            text-shadow: 0 1px 1px rgba(0, 0, 0, 0.02);
        }

        .programs {
            display: grid;
            grid-template-columns: repeat(3, 1fr); /* Fixed 3 columns for landscape */
            grid-template-rows: repeat(2, 1fr); /* 2 rows for 6 programs */
            gap: 30px;
            padding: 30px 50px;
            background: linear-gradient(to bottom, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.95));
            box-shadow: 0 0 30px rgba(69, 104, 220, 0.05) inset;
            flex: 1;
            overflow-y: auto; /* Allow scrolling if needed */
            height: 50%; /* Proportional for 6ft × 3ft */
        }

        .program {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08),
                        0 0 0 1px rgba(255, 255, 255, 0.1),
                        0 0 20px rgba(69, 104, 220, 0.1);
            transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .program:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12),
                        0 0 0 1px rgba(255, 255, 255, 0.2),
                        0 0 30px rgba(69, 104, 220, 0.2);
        }

        .program-header {
            position: relative;
            padding: 28px 25px;
            display: flex;
            align-items: center;
        }

        .program:nth-child(1) .program-header { background: linear-gradient(135deg, #12c2e9 0%, #c471ed 50%, #f64f59 100%); }
        .program:nth-child(2) .program-header { background: linear-gradient(135deg, #8E2DE2 0%, #4A00E0 100%); }
        .program:nth-child(3) .program-header { background: linear-gradient(135deg, #0cebeb 0%, #20e3b2 50%, #29ffc6 100%); }
        .program:nth-child(4) .program-header { background: linear-gradient(135deg, #FF416C 0%, #FF4B2B 100%); }
        .program:nth-child(5) .program-header { background: linear-gradient(135deg, #6a3093 0%, #a044ff 100%); }
        .program:nth-child(6) .program-header { background: linear-gradient(135deg, #36D1DC 0%, #5B86E5 100%); }

        .program-number {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: 'Montserrat', sans-serif;
            font-size: 24px;
            font-weight: 700;
            color: #333;
            margin-right: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1),
                        0 0 0 1px rgba(255, 255, 255, 0.3),
                        0 0 10px rgba(255, 255, 255, 0.5);
            border: 1px solid rgba(255, 255, 255, 0.5);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .program-title {
            font-family: 'Montserrat', sans-serif;
            font-size: 26px;
            font-weight: 700;
            color: white;
            letter-spacing: 0.5px;
        }

        .program-content {
            padding: 20px 25px;
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .program-content ul {
            list-style-type: none;
            padding: 0;
            margin: 0 0 10px 0;
            flex: 1;
        }

        .program-content li {
            color: #505050;
            line-height: 1.5;
            margin-bottom: 12px;
            font-size: 20px;
            position: relative;
            padding-left: 30px;
            display: flex;
            align-items: flex-start;
        }

        .program-content li:before {
            content: "✓";
            color: #4568dc;
            font-weight: bold;
            position: absolute;
            left: 0;
            top: 0;
            font-size: 18px;
            text-shadow: 0 0 5px rgba(69, 104, 220, 0.3);
        }

        .program-content p {
            color: #505050;
            line-height: 1.7;
            margin-bottom: 12px;
            font-size: 16px;
        }

        .program-content p:last-child {
            margin-bottom: 0;
        }

        .key-benefit {
            background: rgba(69, 104, 220, 0.08);
            border-left: 4px solid #4568dc;
            padding: 12px 18px;
            margin-top: auto; /* Push to bottom of flex container */
            border-radius: 0 6px 6px 0;
            font-weight: 500;
            color: #333;
            font-size: 20px;
            display: flex;
            align-items: center;
            box-shadow: 0 3px 10px rgba(69, 104, 220, 0.1);
        }

        .key-benefit:before {
            content: "\f084";
            font-family: "Font Awesome 5 Free";
            font-weight: 900;
            margin-right: 10px;
            font-size: 18px;
            color: #4568dc;
        }

        .highlight-text {
            color: #4568dc;
            font-weight: 600;
            text-shadow: 0 0 1px rgba(69, 104, 220, 0.3);
        }

        footer {
            display: grid;
            grid-template-columns: repeat(3, 1fr); /* Fixed 3 columns for landscape */
            gap: 30px;
            padding: 30px 50px;
            background: rgba(248, 249, 250, 0.7);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            color: #505050;
            font-size: 18px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
            flex-shrink: 0;
            height: 15%; /* Proportional for 6ft × 3ft */
        }

        footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiPjxkZWZzPjxwYXR0ZXJuIGlkPSJwYXR0ZXJuIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgcGF0dGVyblVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgcGF0dGVyblRyYW5zZm9ybT0icm90YXRlKDQ1KSI+PHJlY3Qgd2lkdGg9IjEwIiBoZWlnaHQ9IjEwIiBmaWxsPSJyZ2JhKDAsIDAsIDAsIDAuMDIpIi8+PC9wYXR0ZXJuPjwvZGVmcz48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI3BhdHRlcm4pIi8+PC9zdmc+');
            opacity: 0.5;
            z-index: -1;
        }

        .footer-col {
            display: flex;
            flex-direction: column;
            background: rgba(255, 255, 255, 0.5);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            padding: 15px;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05),
                        0 0 0 1px rgba(255, 255, 255, 0.2),
                        0 0 20px rgba(69, 104, 220, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            height: 100%;
        }

        .footer-col:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08),
                        0 0 0 1px rgba(255, 255, 255, 0.3),
                        0 0 30px rgba(69, 104, 220, 0.2);
        }

        .footer-heading {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            font-size: 24px;
            margin-bottom: 15px;
            color: #333;
            position: relative;
            padding-bottom: 10px;
            display: flex;
            align-items: center;
        }

        .footer-heading i {
            color: #4568dc;
            text-shadow: 0 0 5px rgba(69, 104, 220, 0.3);
        }

        .footer-heading::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 40px;
            height: 3px;
            background: linear-gradient(90deg, #4568dc, #b06ab3);
            border-radius: 3px;
        }

        .contact-info p {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
            font-size: 20px;
        }

        .contact-info i {
            margin-right: 15px;
            color: #4568dc;
            font-size: 24px;
            line-height: 1.5;
            width: 24px;
            text-align: center;
            text-shadow: 0 0 5px rgba(69, 104, 220, 0.3);
        }

        .qr-code {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .qr-code-img {
            width: 160px;
            height: 160px;
            margin-bottom: 15px;
            background: white;
            border: 2px solid rgba(255, 255, 255, 0.5);
            border-radius: 16px;
            padding: 10px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08),
                        0 0 0 1px rgba(255, 255, 255, 0.3),
                        0 0 20px rgba(69, 104, 220, 0.15);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .qr-code-img:hover {
            transform: scale(1.05);
            box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12),
                        0 0 0 1px rgba(255, 255, 255, 0.3),
                        0 0 30px rgba(69, 104, 220, 0.3);
        }

        .copyright {
            grid-column: 1 / -1;
            text-align: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            color: #666;
            font-size: 14px;
            font-weight: 500;
        }

        .highlight {
            color: #4568dc;
            font-weight: 600;
            background: linear-gradient(90deg, #4568dc, #b06ab3);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 2px 10px rgba(69, 104, 220, 0.2);
        }

        /* Computer Icons */
        .icon {
            font-family: 'Font Awesome 5 Free';
            font-style: normal;
            font-weight: 900;
        }

        /* Landscape-specific styles for 6ft × 3ft */
        @media (orientation: landscape) {
            .container {
                flex-direction: column;
                height: 95vh;
                max-width: 72in; /* 6ft width */
                max-height: 36in; /* 3ft height */
            }

            .programs {
                grid-template-columns: repeat(3, 1fr);
                overflow-y: auto;
            }

            footer {
                grid-template-columns: repeat(3, 1fr);
            }

            /* Adjust font sizes for large format */
            body {
                font-size: 20px;
            }
        }

        /* Portrait fallback */
        @media (orientation: portrait) {
            .container {
                height: auto;
            }

            .programs {
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            }

            footer {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            }
        }

        @media (max-width: 992px) {
            .header-content {
                flex-direction: column;
                text-align: center;
                padding-left: 0;
            }

            .logo {
                margin-right: 0;
                margin-bottom: 15px;
            }

            .title-container {
                text-align: center;
            }

            header h1 {
                font-size: 36px;
            }

            .admission-banner {
                transform: rotate(0);
                right: 0;
                top: 0;
                width: 100%;
                text-align: center;
            }

            .programs {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            header {
                padding: 30px 20px 20px;
            }

            header h1 {
                font-size: 30px;
            }

            header h2 {
                font-size: 16px;
            }

            .intro {
                padding: 20px;
            }

            .intro h3 {
                font-size: 16px;
            }

            .intro p {
                font-size: 14px;
            }

            .programs {
                padding: 15px;
                gap: 15px;
                grid-template-columns: 1fr;
            }

            footer {
                padding: 20px 15px;
                grid-template-columns: 1fr;
            }
        }

        /* Print button styles */
        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #4568dc 0%, #b06ab3 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 50px;
            cursor: pointer;
            font-family: 'Montserrat', sans-serif;
            font-weight: 600;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2),
                        0 0 0 1px rgba(255, 255, 255, 0.1),
                        0 0 20px rgba(69, 104, 220, 0.3);
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            font-size: 14px;
            letter-spacing: 0.5px;
        }

        /* Adjust print button position in landscape mode */
        @media (orientation: landscape) {
            .print-button {
                top: 15px;
                right: 15px;
            }
        }

        .print-button:hover {
            background: linear-gradient(135deg, #5B86E5 0%, #b06ab3 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25),
                        0 0 0 1px rgba(255, 255, 255, 0.2),
                        0 0 30px rgba(69, 104, 220, 0.4);
        }
    </style>
</head>
<body>
    <!-- Print Button
    <a href="poster_print_colors.html" class="print-button">
        <i class="fas fa-print"></i> Print Version
    </a> -->

    <!-- Background Tech Elements -->
    <div class="bg-elements">
        <div class="bg-element element-1"><i class="fas fa-laptop-code"></i></div>
        <div class="bg-element element-2"><i class="fas fa-keyboard"></i></div>
        <div class="bg-element element-3"><i class="fas fa-desktop"></i></div>
        <div class="bg-element element-4"><i class="fas fa-mobile-alt"></i></div>
        <div class="bg-element element-5"><i class="fas fa-plug"></i></div>
        <div class="bg-element element-6"><i class="fas fa-mouse"></i></div>
        <div class="bg-element element-7"><i class="fas fa-battery-full"></i></div>
        <div class="bg-element element-8"><i class="fas fa-chart-bar"></i></div>
        <div class="bg-element element-9"><i class="fas fa-search"></i></div>
        <div class="bg-element element-10"><i class="fas fa-cogs"></i></div>
    </div>

    <div class="container">
        <div class="admission-banner">ADMISSIONS OPEN</div>

        <header>
            <div class="header-content">
                <div class="logo">
                    <img src="logo.png" alt="EduCasheer SiliconSpark Logo">
                </div>
                <div class="title-container">
                    <h1>SiliconSpark</h1>
                    <h2>School of Computing and Tech</h2>
                </div>
            </div>
        </header>

        <section class="intro">
            <h3><i class="fas fa-rocket" style="margin-right: 10px; color: #4568dc; text-shadow: 0 0 10px rgba(69, 104, 220, 0.4);"></i>Join our state-of-the-art computer lab, led by <span class="highlight">Musavir Khaliq</span>, a Computer Science researcher with a Master's from IISc.</h3>
            <p>Whether you're a beginner or a tech enthusiast, our dynamic programs will empower you to conquer the digital world and launch your career in technology!</p>
        </section>

        <section class="programs">
            <div class="program">
                <div class="program-header">
                    <div class="program-number">01</div>
                    <div class="program-title"><i class="fas fa-chalkboard-teacher" style="margin-right: 8px;"></i>Learn from Scratch</div>
                </div>
                <div class="program-content">
                    <ul>
                        <li>Introduction to computers for <span class="highlight-text">absolute beginners</span></li>
                        <li>Master basic operations and essential digital literacy skills</li>
                        <li>Learn file management and organization techniques</li>
                    </ul>
                    <div class="key-benefit">No prior experience needed - start your tech journey today!</div>
                </div>
            </div>

            <div class="program">
                <div class="program-header">
                    <div class="program-number">02</div>
                    <div class="program-title"><i class="fas fa-code" style="margin-right: 8px;"></i>Learn to Code</div>
                </div>
                <div class="program-content">
                    <ul>
                        <li>In-depth courses in <span class="highlight-text">Python, JavaScript, C, C++</span> and more</li>
                        <li>Master ethical hacking to protect and secure digital systems</li>
                        <li>Build real-world projects for your portfolio</li>
                    </ul>
                    <div class="key-benefit">Become job-ready with industry-relevant programming skills</div>
                </div>
            </div>

            <div class="program">
                <div class="program-header">
                    <div class="program-number">03</div>
                    <div class="program-title"><i class="fas fa-globe" style="margin-right: 8px;"></i>Build the Web</div>
                </div>
                <div class="program-content">
                    <ul>
                        <li>Create <span class="highlight-text">dynamic, user-friendly websites</span> from scratch</li>
                        <li>Develop innovative mobile and desktop applications</li>
                        <li>Master modern frameworks like React, Angular and Vue</li>
                    </ul>
                    <div class="key-benefit">Launch your career as a full-stack web developer</div>
                </div>
            </div>

            <div class="program">
                <div class="program-header">
                    <div class="program-number">04</div>
                    <div class="program-title"><i class="fas fa-brain" style="margin-right: 8px;"></i>Explore AI & Beyond</div>
                </div>
                <div class="program-content">
                    <ul>
                        <li>Dive into <span class="highlight-text">AI, Machine Learning, and Deep Learning</span></li>
                        <li>Build intelligent systems that learn and adapt</li>
                        <li>Explore cutting-edge technologies shaping our future</li>
                    </ul>
                    <div class="key-benefit">Prepare for the most in-demand tech careers of tomorrow</div>
                </div>
            </div>

            <div class="program">
                <div class="program-header">
                    <div class="program-number">05</div>
                    <div class="program-title"><i class="fas fa-microchip" style="margin-right: 8px;"></i>Computer Hardware</div>
                </div>
                <div class="program-content">
                    <ul>
                        <li>Understand the <span class="highlight-text">inner workings of computers</span></li>
                        <li>Participate in hands-on workshops to assemble custom PCs</li>
                        <li>Develop skills to create drones and other smart devices</li>
                    </ul>
                    <div class="key-benefit">Gain practical hardware skills for tech support and IoT development</div>
                </div>
            </div>

            <div class="program">
                <div class="program-header">
                    <div class="program-number">06</div>
                    <div class="program-title"><i class="fas fa-briefcase" style="margin-right: 8px;"></i>Career Counseling</div>
                </div>
                <div class="program-content">
                    <ul>
                        <li>Gain insights from <span class="highlight-text">industry leaders</span> and tech visionaries</li>
                        <li>Receive personalized guidance for your tech career path</li>
                        <li>Build a professional network with our industry connections</li>
                    </ul>
                    <div class="key-benefit">Transform your tech skills into a rewarding career</div>
                </div>
            </div>
        </section>

        <footer>
            <div class="footer-col contact-info">
                <h3 class="footer-heading"><i class="fas fa-address-book" style="margin-right: 8px;"></i>Contact Us</h3>
                <p><i class="fas fa-phone-alt"></i> +91 8825063816 | +91 7789681275 | +91 8082775767</p>
                <p><i class="fas fa-envelope"></i> <EMAIL></p>
                <p><i class="fas fa-globe"></i> www.educasheer.in</p>
            </div>

            <div class="footer-col contact-info">
                <h3 class="footer-heading"><i class="fas fa-map-marked-alt" style="margin-right: 8px;"></i>Address</h3>
                <p><i class="fas fa-map-marker-alt"></i> EduCasheer SiliconSpark Lab<br>
                New Colony, Jahangeerpore<br>
                Shrakwara, Baramulla Kashmir</p>
            </div>

            <div class="footer-col qr-code">
                <h3 class="footer-heading"><i class="fas fa-qrcode" style="margin-right: 8px;"></i>Scan for Details</h3>
                <div class="qr-code-img">
                    <img src="QRcode.png" alt="QR Code" width="130" height="130" style="border: 3px solid #4568dc; border-radius: 8px; padding: 5px; box-shadow: 0 0 15px rgba(69, 104, 220, 0.4);">
                </div>
                <p>Scan to learn more about our programs</p>
            </div>

            <!-- <div class="copyright">
                <p>© 2025 EduCasheer SiliconSpark. All Rights Reserved.</p>
            </div> -->
        </footer>
    </div>
</body>
</html>