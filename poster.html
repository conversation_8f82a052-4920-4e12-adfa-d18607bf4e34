<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sukoon Sphere - A Sanctuary for Mental Well-being</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Montserrat', sans-serif;
        }

        body {
            background: #000;
            color: #fff;
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
            padding: 20px;
        }

        .poster-container {
            position: relative;
            background: linear-gradient(135deg, #1a2a6c 0%, #b21f1f 50%, #fdbb2d 100%);
            border: 12px solid #000;
            outline: 3px solid #fdbb2d;
            box-shadow: 0 0 40px rgba(0, 0, 0, 0.8);
            width: 1080px;
            margin: 0 auto;
            position: relative;
            overflow: visible;
            max-width: 100%;
            box-sizing: border-box;
        }

        .poster-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="2"/></svg>');
            opacity: 0.3;
            pointer-events: none;
            z-index: -1;
        }

        .poster-corner {
            position: absolute;
            width: 50px;
            height: 50px;
            z-index: 10;
        }

        .corner-top-left {
            top: 0;
            left: 0;
            border-top: 5px solid #fdbb2d;
            border-left: 5px solid #fdbb2d;
        }

        .corner-top-right {
            top: 0;
            right: 0;
            border-top: 5px solid #fdbb2d;
            border-right: 5px solid #fdbb2d;
        }

        .corner-bottom-left {
            bottom: 0;
            left: 0;
            border-bottom: 5px solid #fdbb2d;
            border-left: 5px solid #fdbb2d;
        }

        .corner-bottom-right {
            bottom: 0;
            right: 0;
            border-bottom: 5px solid #fdbb2d;
            border-right: 5px solid #fdbb2d;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 0 30px;
            display: block;
            box-sizing: border-box;
        }

        header {
            padding: 20px 0 10px;
            text-align: center;
            position: relative;
            background: linear-gradient(180deg, rgba(209, 200, 200, 0.5) 0%, rgba(0,0,0,0) 100%);
            border-bottom: 2px solid rgba(255, 255, 255, 0.15);
            margin-bottom: 10px;
        }

        header::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 60%;
            height: 1px;
            background: linear-gradient(90deg, rgba(216, 208, 208, 0) 0%, rgba(255,255,255,0.5) 50%, rgba(255,255,255,0) 100%);
        }

        .logo {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 5px;
            position: relative;
            padding: 5px 0;
        }

        .logo::before, .logo::after {
            content: '';
            position: absolute;
            height: 2px;
            width: 20%;
            background: linear-gradient(90deg, rgba(253,187,45,0) 0%, rgba(253,187,45,1) 50%, rgba(253,187,45,0) 100%);
        }

        .logo::before {
            top: 0;
            left: 50%;
            transform: translateX(-50%);
        }

        .logo::after {
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
        }

        .logo-container {
            position: relative;
            margin-bottom: 2px;
        }

        .logo-container::before {
            content: '★';
            position: absolute;
            left: -15px;
            top: 50%;
            transform: translateY(-50%);
            color: #fdbb2d;
            font-size: 16px;
            animation: starPulse 2s infinite alternate;
        }

        .logo-container::after {
            content: '★';
            position: absolute;
            right: -15px;
            top: 50%;
            transform: translateY(-50%);
            color: #fdbb2d;
            font-size: 16px;
            animation: starPulse 2s infinite alternate-reverse;
        }

        @keyframes starPulse {
            0% { opacity: 0.5; transform: translateY(-50%) scale(0.8); }
            100% { opacity: 1; transform: translateY(-50%) scale(1.1); }
        }

        .logo-img {
            width: 500px;
            height: 300px;
            filter: drop-shadow(0 0 15px rgba(253, 187, 45, 0.7));
            animation: logoPulse 4s infinite ease-in-out;
            max-width: 90%;
            position: relative;
            z-index: 2;
        }

        .logo-glow {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 90%;
            height: 90%;
            background: radial-gradient(circle, rgba(253,187,45,0.3) 0%, rgba(253,187,45,0) 70%);
            z-index: 1;
            filter: blur(15px);
            animation: glowPulse 4s infinite alternate;
        }

        @keyframes logoPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.03); }
        }

        @keyframes glowPulse {
            0% { opacity: 0.4; transform: translate(-50%, -50%) scale(0.9); }
            100% { opacity: 0.7; transform: translate(-50%, -50%) scale(1.1); }
        }

        .logo-text {
            font-size: 36px;
            font-weight: 800;
            letter-spacing: 3px;
            font-family: 'Playfair Display', serif;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.6);
            background: linear-gradient(to right, #fdbb2d, #f39c12, #fdbb2d);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            margin-bottom: 2px;
            line-height: 1;
            text-align: center;
            position: relative;
            display: inline-block;
        }

        .logo-text::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, rgba(253,187,45,0) 0%, rgba(253,187,45,1) 50%, rgba(253,187,45,0) 100%);
        }

        .tagline {
            font-size: 30px;
            font-style: italic;
            text-align: center;
            margin-bottom: 15px;
            color: #fff;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.6);
            position: relative;
            display: inline-block;
            font-weight: 500;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 30px;
            padding: 6px 25px;
            border: 1px solid rgba(253, 187, 45, 0.3);
        }

        .tagline::before, .tagline::after {
            content: '~';
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            color: #fdbb2d;
            font-size: 24px;
        }

        .tagline::before {
            left: 0;
        }

        .tagline::after {
            right: 0;
        }

        .hero {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            padding: 20px 0;
            position: relative;
        }

        .hero::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 60%;
            height: 3px;
            background: linear-gradient(90deg, rgba(253,187,45,0) 0%, rgba(253,187,45,0.8) 50%, rgba(253,187,45,0) 100%);
        }

        .hero h1 {
            font-size: 48px;
            margin-bottom: 30px;
            font-weight: 700;
            line-height: 1.2;
            text-transform: uppercase;
            background: linear-gradient(to right, #fdbb2d, #f39c12, #fdbb2d);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            letter-spacing: 1px;
        }

        .mission {
            max-width: 800px;
            margin: 0 auto 50px;
            text-align: center;
            font-size: 20px;
            line-height: 1.6;
            position: relative;
            z-index: 1;
            background: rgba(0, 0, 0, 0.3);
            padding: 25px;
            border-radius: 15px;
            border: 2px solid rgba(255, 255, 255, 0.1);
        }

        .mission p {
            color: #ffffff;
            text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
            font-weight: 500;
        }

        .wisdom-quote {
            font-size: 26px;
            font-style: italic;
            margin: 30px 0;
            color: #ffffff;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
            font-family: 'Playfair Display', serif;
            position: relative;
            padding: 20px 30px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
            border-left: 4px solid #fdbb2d;
            border-right: 4px solid #fdbb2d;
        }

        .wisdom-quote::before, .wisdom-quote::after {
            content: '"';
            position: absolute;
            font-size: 80px;
            color: rgba(253, 187, 45, 0.3);
            line-height: 0;
        }

        .wisdom-quote::before {
            top: 30px;
            left: 0;
        }

        .wisdom-quote::after {
            bottom: -10px;
            right: 0;
            transform: rotate(180deg);
        }

        .main-image-container {
            position: relative;
            width: 90%;
            max-width: 800px;
            margin: 30px auto;
            text-align: center;
            padding: 20px;
            background: rgba(0, 0, 0, 0.4);
            border-radius: 20px;
            border: 4px solid rgba(255, 255, 255, 0.25);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
        }

        .main-image-container::before {
            content: '';
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            background: linear-gradient(45deg, #fdbb2d, #b21f1f, #1a2a6c, #fdbb2d);
            background-size: 400% 400%;
            z-index: -1;
            border-radius: 25px;
            filter: blur(15px);
            opacity: 0.6;
            animation: gradientBG 15s ease infinite;
        }

        @keyframes gradientBG {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .main-image {
            width: 100%;
            max-width: 1500px;
            border-radius: 12px;
            filter: drop-shadow(0 20px 40px rgba(0, 0, 0, 0.4));
            transition: transform 0.5s ease, filter 0.5s ease;
            border: 3px solid rgba(255, 255, 255, 0.3);
        }

        .main-image:hover {
            transform: scale(1.03);
            filter: drop-shadow(0 25px 50px rgba(0, 0, 0, 0.5)) brightness(1.15);
        }

        .emotions-container {
            width: 100%;
            max-width: 800px;
            margin: 0 auto 30px;
            text-align: center;
            position: relative;
        }

        .emotions-container::before {
            content: 'EMOTIONAL SPECTRUM';
            position: absolute;
            top: -30px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 14px;
            letter-spacing: 3px;
            color: #fdbb2d;
            font-weight: 600;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }

        .emotions {
            display: flex;
            justify-content: center;
            margin: 20px 0 60px;
            flex-wrap: wrap;
            position: relative;
            padding: 20px 0;
        }

        .emotions::before {
            content: '';
            position: absolute;
            top: 0;
            left: 20%;
            right: 20%;
            height: 1px;
            background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.5) 50%, rgba(255,255,255,0) 100%);
        }

        .emotions::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 20%;
            right: 20%;
            height: 1px;
            background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.5) 50%, rgba(255,255,255,0) 100%);
        }

        .emotion {
            padding: 12px 25px;
            margin: 8px;
            font-weight: 600;
            letter-spacing: 2px;
            position: relative;
            overflow: hidden;
            z-index: 1;
            transition: all 0.4s ease;
        }

        .emotion:nth-child(1) {
            background: linear-gradient(135deg, rgba(41, 128, 185, 0.8), rgba(0, 0, 0, 0.6));
            box-shadow: 0 6px 20px rgba(41, 128, 185, 0.5);
            border: 2px solid rgba(41, 128, 185, 0.7);
            border-radius: 30px 10px 30px 10px;
            color: #ffffff;
            font-weight: 700;
            text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
        }

        .emotion:nth-child(2) {
            background: linear-gradient(135deg, rgba(192, 57, 43, 0.8), rgba(0, 0, 0, 0.6));
            box-shadow: 0 6px 20px rgba(192, 57, 43, 0.5);
            border: 2px solid rgba(192, 57, 43, 0.7);
            border-radius: 10px 30px 10px 30px;
            color: #ffffff;
            font-weight: 700;
            text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
        }

        .emotion:nth-child(3) {
            background: linear-gradient(135deg, rgba(142, 68, 173, 0.8), rgba(0, 0, 0, 0.6));
            box-shadow: 0 6px 20px rgba(142, 68, 173, 0.5);
            border: 2px solid rgba(142, 68, 173, 0.7);
            border-radius: 30px 10px 30px 10px;
            color: #ffffff;
            font-weight: 700;
            text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
        }

        .emotion:nth-child(4) {
            background: linear-gradient(135deg, rgba(39, 174, 96, 0.8), rgba(0, 0, 0, 0.6));
            box-shadow: 0 6px 20px rgba(39, 174, 96, 0.5);
            border: 2px solid rgba(39, 174, 96, 0.7);
            border-radius: 10px 30px 10px 30px;
            color: #ffffff;
            font-weight: 700;
            text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
        }

        .emotion::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(120deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.3) 50%, rgba(255,255,255,0) 100%);
            transform: translateX(-100%);
            transition: transform 0.6s ease;
            z-index: -1;
        }

        .emotion:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
            letter-spacing: 3px;
        }

        .emotion:hover::before {
            transform: translateX(100%);
        }

        .core-mission {
            text-align: center;
            margin-bottom: 40px;
            position: relative;
            padding-top: 20px;
        }

        .core-mission::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 3px;
            background: linear-gradient(90deg, rgba(253,187,45,0) 0%, rgba(253,187,45,1) 50%, rgba(253,187,45,0) 100%);
        }

        .core-mission h2 {
            font-size: 36px;
            margin-bottom: 30px;
            font-weight: 700;
            color: #ffffff;
            text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.7);
            position: relative;
            display: inline-block;
            padding: 8px 25px;
            text-transform: uppercase;
            letter-spacing: 2px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            border-bottom: 3px solid #fdbb2d;
        }

        .core-mission h2::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.8) 50%, rgba(255,255,255,0) 100%);
        }

        .values {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin: 30px 0;
            position: relative;
            z-index: 1;
        }

        @media (max-width: 900px) {
            .values {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        .values::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(90deg, rgba(253,187,45,0) 0%, rgba(253,187,45,0.3) 50%, rgba(253,187,45,0) 100%);
            z-index: -1;
        }

        .value-card {
            border-radius: 15px;
            padding: 20px 15px;
            text-align: center;
            transition: all 0.4s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
            z-index: 1;
            height: 100%;
        }

        .value-card:nth-child(1) {
            background: linear-gradient(135deg, rgba(41, 128, 185, 0.3), rgba(0, 0, 0, 0.5));
            box-shadow: 0 8px 32px rgba(41, 128, 185, 0.4);
            border: 2px solid rgba(41, 128, 185, 0.5);
        }

        .value-card:nth-child(2) {
            background: linear-gradient(135deg, rgba(142, 68, 173, 0.3), rgba(0, 0, 0, 0.5));
            box-shadow: 0 8px 32px rgba(142, 68, 173, 0.4);
            border: 2px solid rgba(142, 68, 173, 0.5);
        }

        .value-card:nth-child(3) {
            background: linear-gradient(135deg, rgba(39, 174, 96, 0.3), rgba(0, 0, 0, 0.5));
            box-shadow: 0 8px 32px rgba(39, 174, 96, 0.4);
            border: 2px solid rgba(39, 174, 96, 0.5);
        }

        .value-card:nth-child(4) {
            background: linear-gradient(135deg, rgba(211, 84, 0, 0.3), rgba(0, 0, 0, 0.5));
            box-shadow: 0 8px 32px rgba(211, 84, 0, 0.4);
            border: 2px solid rgba(211, 84, 0, 0.5);
        }

        .value-card:nth-child(5) {
            background: linear-gradient(135deg, rgba(231, 76, 60, 0.3), rgba(0, 0, 0, 0.5));
            box-shadow: 0 8px 32px rgba(231, 76, 60, 0.4);
            border: 2px solid rgba(231, 76, 60, 0.5);
        }

        .value-card:nth-child(6) {
            background: linear-gradient(135deg, rgba(52, 152, 219, 0.3), rgba(0, 0, 0, 0.5));
            box-shadow: 0 8px 32px rgba(52, 152, 219, 0.4);
            border: 2px solid rgba(52, 152, 219, 0.5);
        }

        .value-card:nth-child(7) {
            background: linear-gradient(135deg, rgba(241, 196, 15, 0.3), rgba(0, 0, 0, 0.5));
            box-shadow: 0 8px 32px rgba(241, 196, 15, 0.4);
            border: 2px solid rgba(241, 196, 15, 0.5);
        }

        .value-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%);
            opacity: 0;
            transition: opacity 0.5s ease;
            pointer-events: none;
            z-index: -1;
        }

        .value-card:hover {
            transform: translateY(-10px) scale(1.03);
            box-shadow: 0 15px 45px rgba(0, 0, 0, 0.3);
        }

        .value-card:hover::before {
            opacity: 1;
        }

        .value-icon {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
        }

        .value-card:nth-child(1) .value-icon {
            background: rgba(41, 128, 185, 0.3);
            box-shadow: 0 5px 15px rgba(41, 128, 185, 0.4), inset 0 0 20px rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(41, 128, 185, 0.5);
        }

        .value-card:nth-child(2) .value-icon {
            background: rgba(142, 68, 173, 0.3);
            box-shadow: 0 5px 15px rgba(142, 68, 173, 0.4), inset 0 0 20px rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(142, 68, 173, 0.5);
        }

        .value-card:nth-child(3) .value-icon {
            background: rgba(39, 174, 96, 0.3);
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4), inset 0 0 20px rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(39, 174, 96, 0.5);
        }

        .value-card:nth-child(4) .value-icon {
            background: rgba(211, 84, 0, 0.3);
            box-shadow: 0 5px 15px rgba(211, 84, 0, 0.4), inset 0 0 20px rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(211, 84, 0, 0.5);
        }

        .value-card:nth-child(5) .value-icon {
            background: rgba(231, 76, 60, 0.3);
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4), inset 0 0 20px rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(231, 76, 60, 0.5);
        }

        .value-card:nth-child(6) .value-icon {
            background: rgba(52, 152, 219, 0.3);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4), inset 0 0 20px rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(52, 152, 219, 0.5);
        }

        .value-card:nth-child(7) .value-icon {
            background: rgba(241, 196, 15, 0.3);
            box-shadow: 0 5px 15px rgba(241, 196, 15, 0.4), inset 0 0 20px rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(241, 196, 15, 0.5);
        }

        .value-card:hover .value-icon {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3), inset 0 0 30px rgba(255, 255, 255, 0.3);
        }

        .value-icon img, .value-icon svg {
            width: 45px;
            height: 45px;
            filter: brightness(1.2);
        }

        .icon-svg {
            stroke: #fff;
            transition: all 0.3s ease;
        }

        .value-card:nth-child(1) .icon-svg {
            stroke: rgba(41, 128, 185, 1);
        }

        .value-card:nth-child(2) .icon-svg {
            stroke: rgba(142, 68, 173, 1);
        }

        .value-card:nth-child(3) .icon-svg {
            stroke: rgba(39, 174, 96, 1);
        }

        .value-card:nth-child(4) .icon-svg {
            stroke: rgba(211, 84, 0, 1);
        }

        .value-card:nth-child(5) .icon-svg {
            stroke: rgba(231, 76, 60, 1);
        }

        .value-card:nth-child(6) .icon-svg {
            stroke: rgba(52, 152, 219, 1);
        }

        .value-card:nth-child(7) .icon-svg {
            stroke: rgba(241, 196, 15, 1);
        }

        .value-card:hover .icon-svg {
            transform: scale(1.1);
            filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.3));
        }

        .value-card h3 {
            font-size: 18px;
            margin-bottom: 10px;
            font-weight: 700;
            letter-spacing: 1px;
            position: relative;
            display: inline-block;
            padding-bottom: 8px;
        }

        .value-card h3::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 2px;
            background: #fdbb2d;
        }

        .value-card h3 {
            color: #ffffff;
            text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
            font-weight: 700;
        }

        .value-card:nth-child(1) h3 {
            color: #ffffff;
            border-bottom: 2px solid rgba(41, 128, 185, 1);
        }

        .value-card:nth-child(2) h3 {
            color: #ffffff;
            border-bottom: 2px solid rgba(142, 68, 173, 1);
        }

        .value-card:nth-child(3) h3 {
            color: #ffffff;
            border-bottom: 2px solid rgba(39, 174, 96, 1);
        }

        .value-card:nth-child(4) h3 {
            color: #ffffff;
            border-bottom: 2px solid rgba(211, 84, 0, 1);
        }

        .value-card:nth-child(5) h3 {
            color: #ffffff;
            border-bottom: 2px solid rgba(231, 76, 60, 1);
        }

        .value-card:nth-child(6) h3 {
            color: #ffffff;
            border-bottom: 2px solid rgba(52, 152, 219, 1);
        }

        .value-card:nth-child(7) h3 {
            color: #ffffff;
            border-bottom: 2px solid rgba(241, 196, 15, 1);
        }

        .value-card p {
            font-size: 14px;
            line-height: 1.5;
            color: rgba(255, 255, 255, 1);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            margin: 0;
        }

        .cta {
            text-align: center;
            padding: 30px 0;
            position: relative;
        }

        .cta::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 80%;
            height: 2px;
            background: linear-gradient(90deg, rgba(253,187,45,0) 0%, rgba(253,187,45,0.8) 50%, rgba(253,187,45,0) 100%);
        }

        .cta h2 {
            font-size: 38px;
            margin-bottom: 40px;
            font-weight: 700;
            color: #ffffff;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            position: relative;
            display: inline-block;
            padding: 0 20px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .cta .glass {
            background: rgba(0, 0, 0, 0.5);
            border: 3px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .cta .glass::before {
            content: '';
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            background: linear-gradient(45deg, #1a2a6c, #b21f1f, #fdbb2d, #1a2a6c);
            background-size: 400% 400%;
            z-index: -1;
            border-radius: 20px;
            filter: blur(15px);
            opacity: 0.6;
            animation: gradientBG 15s ease infinite;
        }

        .btn {
            display: inline-block;
            padding: 18px 45px;
            background: linear-gradient(135deg, rgba(253,187,45,0.8), rgba(178,31,31,0.8));
            color: #fff;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 700;
            font-size: 18px;
            letter-spacing: 2px;
            transition: all 0.4s ease;
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            position: relative;
            overflow: hidden;
            z-index: 1;
            text-transform: uppercase;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(253,187,45,0.4), rgba(178,31,31,0.4));
            z-index: -1;
            transition: transform 0.6s cubic-bezier(0.65, 0, 0.35, 1);
            transform: scaleX(0);
            transform-origin: right;
        }

        .btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
            letter-spacing: 3px;
        }

        .btn:hover::before {
            transform: scaleX(1);
            transform-origin: left;
        }

        footer {
            text-align: center;
            padding: 20px 0;
            background: rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(10px);
            border-top: 4px solid rgba(253, 187, 45, 0.4);
            position: relative;
        }

        footer p {
            color: rgba(255, 255, 255, 0.8);
            font-size: 16px;
            letter-spacing: 1px;
        }

        footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.05)"/></svg>');
            opacity: 0.5;
            pointer-events: none;
        }

        /* Glass morphism elements */
        .glass {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(12px);
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.3);
            margin: 30px 0;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .glass:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
        }

        /* Animations */
        @keyframes fadeUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-up {
            animation: fadeUp 0.8s ease forwards;
        }

        /* Additional decorative elements */
        .floating-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            overflow: hidden;
        }

        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(5px);
            border-radius: 50%;
            animation: float 20s infinite ease-in-out;
            box-shadow: inset 0 0 30px rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .shape:nth-child(1) {
            width: 350px;
            height: 350px;
            top: 5%;
            left: -150px;
            animation-delay: 0s;
            background: radial-gradient(circle at 30% 40%, rgba(255, 255, 255, 0.12), rgba(255, 255, 255, 0.03));
        }

        .shape:nth-child(2) {
            width: 250px;
            height: 250px;
            top: 60%;
            right: -100px;
            animation-delay: 3s;
            background: radial-gradient(circle at 70% 60%, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.02));
        }

        .shape:nth-child(3) {
            width: 200px;
            height: 200px;
            bottom: 10%;
            left: 10%;
            animation-delay: 6s;
            background: radial-gradient(circle at 40% 50%, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.02));
        }

        .shape:nth-child(4) {
            width: 150px;
            height: 150px;
            top: 30%;
            right: 20%;
            animation-delay: 9s;
            background: radial-gradient(circle at 60% 30%, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.02));
        }

        @keyframes float {
            0% {
                transform: translate(0, 0) rotate(0deg);
            }
            25% {
                transform: translate(15px, 25px) rotate(5deg);
            }
            50% {
                transform: translate(25px, 45px) rotate(10deg);
            }
            75% {
                transform: translate(15px, 25px) rotate(5deg);
            }
            100% {
                transform: translate(0, 0) rotate(0deg);
            }
        }

        /* Responsive styles */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 36px;
            }

            .mission {
                font-size: 16px;
            }

            .values {
                grid-template-columns: 1fr;
            }

            .wisdom-quote {
                font-size: 20px;
            }
        }
    </style>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;600;700&family=Playfair+Display:wght@700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="poster-container">
        <div class="poster-corner corner-top-left"></div>
        <div class="poster-corner corner-top-right"></div>
        <div class="poster-corner corner-bottom-left"></div>
        <div class="poster-corner corner-bottom-right"></div>

        <div class="floating-shapes">
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
        </div>

        <div class="container">
        <!-- <header class="fade-up"> -->
            <div class="logo">
                <div class="logo-container">
                    <!-- <div class="logo-glow"></div> -->
                    <img src="Sukoon__1_-removebg.png" alt="Sukoon Sphere Logo" class="logo-img">
                </div>
                <!-- <div class="logo-text">SUKOON SPHERE</div> -->
            </div>
            <div class="tagline">"We don't heal in isolation, but in community via reciprocal learning and active listening."</div>
        <!-- </header> -->

        <!-- <section class="hero fade-up" style="animation-delay: 0.2s;">
            <h1>Where You Discover Yourself, We Help You</h1>
            <div class="mission glass">
                <p>Our mission is to foster reciprocal learning, active listening, and scientific understanding of the brain to help people. We promote respect for diverse viewpoints and resilience in navigating a world that cannot be changed.</p>
            </div>
        </section> -->

        <div class="main-image-container fade-up" style="animation-delay: 0.4s;">
            <img src="image.png" alt="Sukoon Sphere Main Image" class="main-image">
        </div>

        <!-- <div class="wisdom-quote fade-up" style="animation-delay: 0.6s;">
            "Turn Your Wounds Into Wisdom"
        </div> -->

        <div class="emotions-container fade-up" style="animation-delay: 0.8s;">
            <div class="emotions">
                <div class="emotion">FEAR</div>
                <div class="emotion">RAGE</div>
                <div class="emotion">PANIC</div>
                <div class="emotion">SEEKING</div>
            </div>
        </div>

        <section class="core-mission fade-up" style="animation-delay: 1s;">
            <h2>Our Core Mission</h2>
            <div class="values">
                <div class="value-card">
                    <div class="value-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon-svg">
                            <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
                            <path d="M2 17l10 5 10-5"></path>
                            <path d="M2 12l10 5 10-5"></path>
                        </svg>
                    </div>
                    <h3>Reciprocal Learning</h3>
                    <p>We learn from each other through shared experiences and knowledge exchange.</p>
                </div>
                <div class="value-card">
                    <div class="value-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon-svg">
                            <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
                        </svg>
                    </div>
                    <h3>Resilience Building</h3>
                    <p>Developing inner strength to navigate life's challenges with grace.</p>
                </div>
                <div class="value-card">
                    <div class="value-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon-svg">
                            <path d="M3 18v-6a9 9 0 0 1 18 0v6"></path>
                            <path d="M21 19a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3zM3 19a2 2 0 0 0 2 2h1a2 2 0 0 0 2-2v-3a2 2 0 0 0-2-2H3z"></path>
                        </svg>
                    </div>
                    <h3>Active Listening</h3>
                    <p>Truly hearing others with empathy and understanding.</p>
                </div>
                <div class="value-card">
                    <div class="value-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon-svg">
                            <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                            <line x1="8" y1="21" x2="16" y2="21"></line>
                            <line x1="12" y1="17" x2="12" y2="21"></line>
                        </svg>
                    </div>
                    <h3>Modern Technology</h3>
                    <p>Leveraging innovations to enhance mental well-being practices.</p>
                </div>
                <div class="value-card">
                    <div class="value-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon-svg">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                            <polyline points="14 2 14 8 20 8"></polyline>
                            <line x1="16" y1="13" x2="8" y2="13"></line>
                            <line x1="16" y1="17" x2="8" y2="17"></line>
                            <polyline points="10 9 9 9 8 9"></polyline>
                        </svg>
                    </div>
                    <h3>Research Oriented Approach</h3>
                    <p>Basing our methods on scientific understanding of the brain.</p>
                </div>
                <div class="value-card">
                    <div class="value-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon-svg">
                            <circle cx="12" cy="12" r="10"></circle>
                            <path d="M8 14s1.5 2 4 2 4-2 4-2"></path>
                            <line x1="9" y1="9" x2="9.01" y2="9"></line>
                            <line x1="15" y1="9" x2="15.01" y2="9"></line>
                        </svg>
                    </div>
                    <h3>Culture of Discussion</h3>
                    <p>Creating safe spaces for open dialogue and diverse perspectives.</p>
                </div>
                <div class="value-card">
                    <div class="value-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon-svg">
                            <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path>
                        </svg>
                    </div>
                    <h3>Open Communication</h3>
                    <p>Fostering transparency and honest exchanges in our community.</p>
                </div>
            </div>
        </section>

        <section class="cta fade-up" style="animation-delay: 1.2s;">
            <div class="glass">
                <h2>Join Sukoon Sphere: A sanctuary for mental well-being</h2>
                <a href="https://www.sukoonsphere.org" class="btn">Visit www.sukoonsphere.org</a>
            </div>
        </section>
    </div>

    <!-- <footer>
        <p>&copy; 2025 Sukoon Sphere. All rights reserved.</p>
    </footer> -->
    </div> <!-- End of poster-container -->

    <script>
        // Simple scroll animation
        document.addEventListener('DOMContentLoaded', function() {
            const elements = document.querySelectorAll('.fade-up');

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, {
                threshold: 0.1
            });

            elements.forEach(element => {
                element.style.opacity = '0';
                element.style.transform = 'translateY(20px)';
                element.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
                observer.observe(element);
            });

            // Handle emotions hover effect
            const emotions = document.querySelectorAll('.emotion');
            emotions.forEach(emotion => {
                emotion.addEventListener('mouseover', function() {
                    this.style.background = 'rgba(255, 255, 255, 0.3)';
                });
                emotion.addEventListener('mouseout', function() {
                    this.style.background = 'rgba(255, 255, 255, 0.1)';
                });
            });

            // Add parallax effect to floating shapes
            window.addEventListener('mousemove', function(e) {
                const shapes = document.querySelectorAll('.shape');
                const x = e.clientX / window.innerWidth;
                const y = e.clientY / window.innerHeight;

                shapes.forEach((shape, index) => {
                    const speed = 30 / (index + 1);
                    const xPos = (0.5 - x) * speed;
                    const yPos = (0.5 - y) * speed;

                    shape.style.transform = `translate(${xPos}px, ${yPos}px) rotate(${xPos * 0.5}deg)`;
                });
            });
        });
    </script>
</body>
</html>